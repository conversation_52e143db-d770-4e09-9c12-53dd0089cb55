version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/bioconda/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/aragorn-1.2.41-h7b50bb2_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/bowtie-1.3.1-py39h9046dc2_10.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.11-h5ca1c30_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/falco-1.2.5-h077b44d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/htslib-1.21-h566b1c6_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/infernal-1.1.5-pl5321h7b50bb2_4.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.23.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.13.0-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.23-h4ddbbb0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.23.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.1-hee588c1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hf672d98_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-h8f9b012_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-14.2.0-h4852527_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.7-h81593ed_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/linearfold-1.0.1.dev20220829-h9948957_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/megahit-1.2.9-h5ca1c30_6.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncbi-datasets-cli-17.3.0-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pigz-2.8-hadc24fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.0.1-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/plass-5.cf8933-pl5321h6a68c12_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.9.22-h85ef794_1_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gflags-3.1.2-py_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.9-6_cp39.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/seqkit-2.10.0-h9ee0642_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-78.1.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/spades-4.1.0-haf24da9_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/trnascan-se-2.0.12-pl5321h7b50bb2_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/d2/8d/2733707a9daf4df14cb6ae251691cfc49fcce888234ec6484364d8319b46/archspec-0.2.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/81/af/daf15b69f4ced15c1632bbbdff87ba74f7074d100078477a3a5c1a31334b/bbmapy-0.0.46-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/04/cbbbf16108eb6af46ab5a2cc366a10694847dabb96817291ef214e052423/biocframe-0.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/16/45/70ac20e09eaf043968fb92d0db3468ef29c593e83275418ea2905a9959dc/biocutils-0.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5a/6d/e2773862b043dcf8a221342954f375392bb2ce6487bcd9f2c1b34e1d6781/charset_normalizer-3.4.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/e8/11ef9ea800061598dd4a2229c408d985c771ee52d6fce7efdc65d3375459/genomicranges-0.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/5e/af84054b0ff9f9fbe49a7079d46ba8b4ee7ab6192a0310d4bd2c91254626/install_jdk-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/50/fb/396d568039d21344639db96d940d40eb62befe704ef849b27949ded5c3bb/intervaltree-3.1.0.tar.gz
      - pypi: https://files.pythonhosted.org/packages/eb/1d/0cda9eb4036f3bc08e315c0f7af3c514a890fa2e89958575555b2de9530d/IRanges-0.4.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/97/0d/f74b301ea0c44068274a0c95815e1a183a0c9af5ea32db817d8b3d33af36/lightmotif-0.9.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/90/48/9818341371f20a2644267a1769aaecb7642f74839192afad278a38e0fe14/mappy-2.28.tar.gz
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/85/86/10fbefe30a47c36d041966d9ddbb208fcfd15cf02bc2e57abac9fab186cd/ncls-0.0.68-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/33/83/5f50f1dac42953e64588be292904fcaede10439323b0a859e1caf9b43ab1/needletail-0.6.3.tar.gz
      - pypi: https://files.pythonhosted.org/packages/b9/14/78635daab4b07c0930c919d451b8bf8c164774e6a3413aed04a6d95758ce/numpy-2.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/20/c1/c65924c0ca186f481c02b531f1ec66c34f9bbecc11d70246562bb4949876/polars-1.27.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/9c/39/0f88a830a1c8a3aba27fededc642da37613c57cbff143412e3536f89784f/psutil-6.1.1-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/58/61/56b32f2825759f3f0c796567fbe89c3f8462a6db7105ec30b30f2a96b88e/pyfastx-2.2.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8a/0b/9fcc47d19c48b59121088dd6da2488a49d5f72dacf8262e2790a1d2c7d15/pygments-2.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/3a/78b667b16d55fa50cb2130a8c68c810bfa56ac3293a39c4bbea829bbc3b5/pyhmmer-0.11.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/19/d1/853ea09b04a48bc74005592bbcd284dfafc717c477d71a145535b293dddb/pyrodigal-3.6.3.post1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d6/8f/e78b92bc836a7b50a3497ee655983561dec4ac2df06136f0c55e59a00144/pyrodigal_gv-0.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fa/69/963f0bf44a654f6465bdb66fb5a91051b0d7af9f742b5bd7202607165036/rich_click-1.8.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/32/46/9cb0e58b2deb7f82b84065f37f3bffeb12413f947f9388e4cac22c4621ce/sortedcontainers-2.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8b/54/b1ae86c0973cc6f0210b53d508ca3641fb6d0c56823f288d108bc7ab3cc8/typing_extensions-4.13.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6b/11/cc635220681e93a0183390e26485430ca2c7b5f9d33b15c74c2861cb8091/urllib3-2.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ef/25/b254e7c48f851be16e69d3ed8f7b67922353a870490533b1512b85e06b7c/ViennaRNA-2.7.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: .
  dev:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/bioconda/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/aragorn-1.2.41-h7b50bb2_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/bowtie-1.3.1-py312hf8dbd9f_10.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.11-h5ca1c30_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/falco-1.2.5-h077b44d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/htslib-1.21-h566b1c6_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/infernal-1.1.5-pl5321h7b50bb2_4.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.23.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.13.0-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.23-h4ddbbb0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.23.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.1-hee588c1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hf672d98_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-h8f9b012_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-14.2.0-h4852527_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.7-h81593ed_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/linearfold-1.0.1.dev20220829-h9948957_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/megahit-1.2.9-h5ca1c30_6.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncbi-datasets-cli-17.3.0-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pigz-2.8-hadc24fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.0.1-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/plass-5.cf8933-pl5321h6a68c12_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.10-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gflags-3.1.2-py_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.12-6_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/seqkit-2.10.0-h9ee0642_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-78.1.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/spades-4.1.0-haf24da9_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/trnascan-se-2.0.12-pl5321h7b50bb2_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d2/8d/2733707a9daf4df14cb6ae251691cfc49fcce888234ec6484364d8319b46/archspec-0.2.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/81/af/daf15b69f4ced15c1632bbbdff87ba74f7074d100078477a3a5c1a31334b/bbmapy-0.0.46-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/04/cbbbf16108eb6af46ab5a2cc366a10694847dabb96817291ef214e052423/biocframe-0.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/16/45/70ac20e09eaf043968fb92d0db3468ef29c593e83275418ea2905a9959dc/biocutils-0.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/3e/a2/513f6cbe752421f16d969e32f3583762bfd583848b763913ddab8d9bfd4f/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/2b/999b2a1e1ba2206f2d3bca267d68f350beb2b048a41ea827e08ce7260098/cryptography-44.0.2-cp39-abi3-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8f/d7/9322c609343d929e75e7e5e6255e614fcc67572cfd083959cdef3b7aad79/docutils-0.21.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/e8/11ef9ea800061598dd4a2229c408d985c771ee52d6fce7efdc65d3375459/genomicranges-0.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a5/40/19c0935bf9f25808541a0e3144ac459de696c5b6b6d4511a98d456c69604/hatch-1.14.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/08/e7/ae38d7a6dfba0533684e0b2136817d667588ae3ec984c1a4e5df5eb88482/hatchling-1.27.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/8d/f052b1e336bb2c1fc7ed1aaed898aa570c0b61a09707b108979d9fc6e308/httpcore-1.0.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6e/aa/8caf6a0a3e62863cbb9dab27135660acba46903b703e224f14f447e57934/hyperlink-21.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/cb/18326d2d89ad3b0dd143da971e77afd1e6ca6674f1b1c3df4b6bec6279fc/id-1.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/5e/af84054b0ff9f9fbe49a7079d46ba8b4ee7ab6192a0310d4bd2c91254626/install_jdk-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/50/fb/396d568039d21344639db96d940d40eb62befe704ef849b27949ded5c3bb/intervaltree-3.1.0.tar.gz
      - pypi: https://files.pythonhosted.org/packages/1b/13/c6e618598d3b8f195fc16cd529bba7184758c050d1d379ba4eafe890c7b6/IRanges-0.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7f/66/b15ce62552d84bbfcec9a4873ab79d993a1dd4edb922cbfccae192bd5b5f/jaraco.classes-3.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ff/db/0c52c4cf5e4bd9f5d7135ec7669a3a767af21b3a308e1ed3674881e52b62/jaraco.context-6.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/4f/24b319316142c44283d7540e76c7b5a6dbd5db623abd86bb7b3491c21018/jaraco.functools-4.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b2/a3/e137168c9c44d18eff0376253da9f1e9234d0239e0ee230d2fee6cea8e55/jeepney-0.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d3/32/da7f44bcb1105d3e88a0b74ebdca50c59121d2ddf71c9e34ba47df7f3a56/keyring-25.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ef/c1/d126e62bdf8f7f9ad84660773fb2eab2f9aef9df71d9fd47f1466f47f54e/lightmotif-0.9.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/90/48/9818341371f20a2644267a1769aaecb7642f74839192afad278a38e0fe14/mappy-2.28.tar.gz
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/23/62/0fe302c6d1be1c777cab0616e6302478251dfbf9055ad426f5d0def75c89/more_itertools-10.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/b8/210d5cb1fa85c7675323aacbd52af11553dc190aad1c15584699f40797f1/ncls-0.0.68.tar.gz
      - pypi: https://files.pythonhosted.org/packages/88/03/7c6d78a3a84344c885236401e705280bf29b664215b3a8b1e36f9221036d/needletail-0.6.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/11/a9/1cd3c6964ec51daed7b01ca4686a5c793581bf4492cbd7274b3f544c9abe/nh3-0.2.21-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/02/e2/e2cbb8d634151aab9528ef7b8bab52ee4ab10e076509285602c2a3a686e0/numpy-2.2.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9e/c3/059298687310d527a58bb01f3b1965787ee3b40dce76752eda8b44e9a2c5/pexpect-4.9.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6d/45/59578566b3275b8fd9157885918fcd0c4d74162928a5310926887b856a51/platformdirs-4.3.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/5f/e351af9a41f866ac3f1fac4ca0613908d9a41741cfcf2228f4ad853b697d/pluggy-1.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/20/c1/c65924c0ca186f481c02b531f1ec66c34f9bbecc11d70246562bb4949876/polars-1.27.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/9c/39/0f88a830a1c8a3aba27fededc642da37613c57cbff143412e3536f89784f/psutil-6.1.1-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/22/a6/858897256d0deac81a172289110f31629fc4cee19b6f01283303e18c8db3/ptyprocess-0.7.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1f/9b/860b1f95ed305c0ae42d943cf9006733fd6bbb879ccf21a542e905b5d991/pyfastx-2.2.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8a/0b/9fcc47d19c48b59121088dd6da2488a49d5f72dacf8262e2790a1d2c7d15/pygments-2.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4f/00/029fbc534fdd30f074acd7812e15f8974929459e55dffb312677eafd0c67/pyhmmer-0.11.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/aa/9b/060c0269ff913b2d5c48e22a684a73534a0dfcd9a46d21d450987263d962/pyrodigal-3.6.3.post1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d6/8f/e78b92bc836a7b50a3497ee655983561dec4ac2df06136f0c55e59a00144/pyrodigal_gv-0.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e1/67/921ec3024056483db83953ae8e48079ad62b92db7880013ca77632921dd0/readme_renderer-44.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ff/9a/9afaade874b2fa6c752c36f1548f718b5b83af81ed9b76628329dab81c1b/rfc3986-2.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fa/69/963f0bf44a654f6465bdb66fb5a91051b0d7af9f742b5bd7202607165036/rich_click-1.8.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/61/9f/a3e34de425a668284e7024ee6fd41f452f6fa9d817f1f3495b46e5e3a407/ruff-0.11.6-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/54/24/b4293291fa1dd830f353d2cb163295742fa87f179fcc8a20a306a81978b7/SecretStorage-3.3.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/32/46/9cb0e58b2deb7f82b84065f37f3bffeb12413f947f9388e4cac22c4621ce/sortedcontainers-2.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/18/c86eb8e0202e32dd3df50d43d7ff9854f8e0603945ff398974c1d91ac1ef/tomli_w-1.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f9/b6/a447b5e4ec71e13871be01ba81f5dfc9d0af7e473da256ff46bc0e24026f/tomlkit-0.13.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/70/7d/a2271b98b833680561ab3fcd60ab682478dc4f7cc023fab24991601ac8ac/trove_classifiers-2025.4.11.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7c/b6/74e927715a285743351233f33ea3c684528a0d374d2e43ff9ce9585b73fe/twine-6.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8b/54/b1ae86c0973cc6f0210b53d508ca3641fb6d0c56823f288d108bc7ab3cc8/typing_extensions-4.13.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6b/11/cc635220681e93a0183390e26485430ca2c7b5f9d33b15c74c2861cb8091/urllib3-2.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/43/99/3ec6335ded5b88c2f7ed25c56ffd952546f7ed007ffb1e1539dc3b57015a/userpath-1.9.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/fd/21a82b78173be1a2ea20f4f55154e7252bd80d21ed60b9bbbc0e2047b8d0/uv-0.6.14-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/03/2f/73ea7b15dc226f120dbc89f3f4bb40b6a5a26ac969898255f22123a94d1a/ViennaRNA-2.7.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/4c/ed/3cfeb48175f0671ec430ede81f628f9fb2b1084c9064ca67ebe8c0ed6a05/virtualenv-20.30.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fc/79/edeb217c57fe1bf16d890aa91a1c2c96b28c07b46afed54a5dcf310c3f6f/zstandard-0.23.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: .
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
  name: anyio
  version: 4.9.0
  sha256: 9f76d541cad6e36af7beb62e978876f3b41e3e04f2c1fbf0884604c0a9c4d93c
  requires_dist:
  - exceptiongroup>=1.0.2 ; python_full_version < '3.11'
  - idna>=2.8
  - sniffio>=1.1
  - typing-extensions>=4.5 ; python_full_version < '3.13'
  - trio>=0.26.1 ; extra == 'trio'
  - anyio[trio] ; extra == 'test'
  - blockbuster>=1.5.23 ; extra == 'test'
  - coverage[toml]>=7 ; extra == 'test'
  - exceptiongroup>=1.2.0 ; extra == 'test'
  - hypothesis>=4.0 ; extra == 'test'
  - psutil>=5.9 ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - trustme ; extra == 'test'
  - truststore>=0.9.1 ; python_full_version >= '3.10' and extra == 'test'
  - uvloop>=0.21 ; python_full_version < '3.14' and platform_python_implementation == 'CPython' and sys_platform != 'win32' and extra == 'test'
  - packaging ; extra == 'doc'
  - sphinx~=8.2 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinx-autodoc-typehints>=1.2.0 ; extra == 'doc'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/bioconda/linux-64/aragorn-1.2.41-h7b50bb2_5.tar.bz2
  sha256: b104f25a5b857963e1d5e97b9a3e1e7340433e0ca732ee7f62f3e830ae5415c6
  md5: 4d61210644f1e9a1f5cb97fc35d8018a
  depends:
  - libgcc >=13
  license: GPLv3
  purls: []
  size: 147913
  timestamp: 1740502673505
- pypi: https://files.pythonhosted.org/packages/d2/8d/2733707a9daf4df14cb6ae251691cfc49fcce888234ec6484364d8319b46/archspec-0.2.5-py3-none-any.whl
  name: archspec
  version: 0.2.5
  sha256: 604bd4115cb4c18e50a22a9b4a1e516706712263790d7d2994aaa595e70082f6
  requires_python: '!=2.7.*,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,>=3.6'
- conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
  sha256: 06ac389ee45049af40aeb9940eacef92f04d6b5741fc1154be282f420479a49f
  md5: 03b8874fa70df577f3eee53085d025cf
  depends:
  - c-ares >=1.28.1,<2.0a0
  - libgcc-ng >=12
  - libsqlite >=3.46.0,<4.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libstdcxx-ng >=12
  - libxml2 >=2.12.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.1,<4.0a0
  license: GPL-2.0-only
  license_family: GPL
  purls: []
  size: 1638055
  timestamp: 1718840932941
- pypi: https://files.pythonhosted.org/packages/81/af/daf15b69f4ced15c1632bbbdff87ba74f7074d100078477a3a5c1a31334b/bbmapy-0.0.46-py3-none-any.whl
  name: bbmapy
  version: 0.0.46
  sha256: ee57c9627c802df85c9ddd1a7ccbdb802c40d764cdf66135c3589b96f11b3d88
  requires_dist:
  - install-jdk
  - rich
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/64/04/cbbbf16108eb6af46ab5a2cc366a10694847dabb96817291ef214e052423/biocframe-0.6.3-py3-none-any.whl
  name: biocframe
  version: 0.6.3
  sha256: 5d05778af7645403febc434dee73f7304060ef1b655c58e7385498b067d33354
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - biocutils>=0.1.4
  - numpy
  - pandas ; extra == 'optional'
  - polars ; extra == 'optional'
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pandas ; extra == 'testing'
  - polars ; extra == 'testing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/16/45/70ac20e09eaf043968fb92d0db3468ef29c593e83275418ea2905a9959dc/biocutils-0.2.2-py3-none-any.whl
  name: biocutils
  version: 0.2.2
  sha256: cf6aa403df089f28a371d2efee06afa544911c0464c9a3c43de260ab421f29fe
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - numpy
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pandas ; extra == 'testing'
  - scipy ; extra == 'testing'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/bioconda/linux-64/bowtie-1.3.1-py312hf8dbd9f_10.tar.bz2
  sha256: 2ea44e6d86e4a73621901b559977afff0eeb698d2693834be915a011b4bd4ed6
  md5: 75b4f2ab20e8a4ff848ed6736d91fbdb
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - perl
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tbb >=2021.13.0
  - zlib
  license: Artistic-2.0-only
  purls: []
  size: 992200
  timestamp: 1733853172460
- conda: https://conda.anaconda.org/bioconda/linux-64/bowtie-1.3.1-py39h9046dc2_10.tar.bz2
  sha256: 06b3d7f173bfcb060857a46d41f974195d34e56cbfe8c8793b2228b4993feea9
  md5: e29114e510c0cf59cd2bbf42039d4b59
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - perl
  - python >=3.9,<3.10.0a0
  - python_abi 3.9.* *_cp39
  - tbb >=2021.13.0
  - zlib
  license: Artistic-2.0-only
  purls: []
  size: 991958
  timestamp: 1733853407316
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
  sha256: bf832198976d559ab44d6cdb315642655547e26d826e34da67cbee6624cda189
  md5: 19f3a56f68d2fd06c516076bff482c52
  license: ISC
  purls: []
  size: 158144
  timestamp: 1738298224464
- pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
  name: certifi
  version: 2025.1.31
  sha256: ca78db4565a652026a4db2bcdf68f2fb589ea80d0be70e03929ed730746b84fe
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: cffi
  version: 1.17.1
  sha256: b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93
  requires_dist:
  - pycparser
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/3e/a2/513f6cbe752421f16d969e32f3583762bfd583848b763913ddab8d9bfd4f/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: charset-normalizer
  version: 3.4.1
  sha256: bc2722592d8998c870fa4e290c2eec2c1569b87fe58618e67d38b4665dfa680d
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/5a/6d/e2773862b043dcf8a221342954f375392bb2ce6487bcd9f2c1b34e1d6781/charset_normalizer-3.4.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: charset-normalizer
  version: 3.4.1
  sha256: 0af291f4fe114be0280cdd29d533696a77b5b49cfde5467176ecab32353395c4
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
  name: click
  version: 8.1.8
  sha256: 63c132bbbed01578a06712a2d1f497bb62d9c1c0d329b7903a866228027263b2
  requires_dist:
  - colorama ; sys_platform == 'win32'
  - importlib-metadata ; python_full_version < '3.8'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/78/2b/999b2a1e1ba2206f2d3bca267d68f350beb2b048a41ea827e08ce7260098/cryptography-44.0.2-cp39-abi3-manylinux_2_28_x86_64.whl
  name: cryptography
  version: 44.0.2
  sha256: b042d2a275c8cee83a4b7ae30c45a15e6a4baa65a179a0ec2d78ebb90e4f6699
  requires_dist:
  - cffi>=1.12 ; platform_python_implementation != 'PyPy'
  - bcrypt>=3.1.5 ; extra == 'ssh'
  - nox>=2024.4.15 ; extra == 'nox'
  - nox[uv]>=2024.3.2 ; python_full_version >= '3.8' and extra == 'nox'
  - cryptography-vectors==44.0.2 ; extra == 'test'
  - pytest>=7.4.0 ; extra == 'test'
  - pytest-benchmark>=4.0 ; extra == 'test'
  - pytest-cov>=2.10.1 ; extra == 'test'
  - pytest-xdist>=3.5.0 ; extra == 'test'
  - pretend>=0.7 ; extra == 'test'
  - certifi>=2024 ; extra == 'test'
  - pytest-randomly ; extra == 'test-randomorder'
  - sphinx>=5.3.0 ; extra == 'docs'
  - sphinx-rtd-theme>=3.0.0 ; python_full_version >= '3.8' and extra == 'docs'
  - pyenchant>=3 ; extra == 'docstest'
  - readme-renderer>=30.0 ; extra == 'docstest'
  - sphinxcontrib-spelling>=7.3.1 ; extra == 'docstest'
  - build>=1.0.0 ; extra == 'sdist'
  - ruff>=0.3.6 ; extra == 'pep8test'
  - mypy>=1.4 ; extra == 'pep8test'
  - check-sdist ; python_full_version >= '3.8' and extra == 'pep8test'
  - click>=8.0.1 ; extra == 'pep8test'
  requires_python: '>=3.7,!=3.9.0,!=3.9.1'
- conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.11-h5ca1c30_1.tar.bz2
  sha256: 7dd9b80afc663e4ed9585128753159e3b61b028d5bef4a3e9ab7ed167de927c9
  md5: 1e0aaeb1fc4011e948e8c63a986cf9d8
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: GPL-3.0-or-later
  license_family: GPL3
  size: 3629255
  timestamp: 1739439887031
- pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
  name: distlib
  version: 0.3.9
  sha256: 47f8c22fd27c27e25a65601af709b38e4f0a45ea4fc2e710f65755fa8caaaf87
- pypi: https://files.pythonhosted.org/packages/8f/d7/9322c609343d929e75e7e5e6255e614fcc67572cfd083959cdef3b7aad79/docutils-0.21.2-py3-none-any.whl
  name: docutils
  version: 0.21.2
  sha256: dafca5b9e384f0e419294eb4d2ff9fa826435bf15f15b7bd45723e8ad76811b2
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/bioconda/linux-64/falco-1.2.5-h077b44d_0.tar.bz2
  sha256: f0a95ce0b725d89fc1d71e9b29c99ee2caeb930fc135ef2ac02dce26673bbdea
  md5: c0871c7427e2de87bfab229401d86d31
  depends:
  - htslib >=1.21,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: GPL-3.0-only
  license_family: GPL3
  purls: []
  size: 198203
  timestamp: 1736350294266
- pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
  name: filelock
  version: 3.18.0
  sha256: c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'testing'
  - coverage>=7.6.10 ; extra == 'testing'
  - diff-cover>=9.2.1 ; extra == 'testing'
  - pytest-asyncio>=0.25.2 ; extra == 'testing'
  - pytest-cov>=6 ; extra == 'testing'
  - pytest-mock>=3.14 ; extra == 'testing'
  - pytest-timeout>=2.3.1 ; extra == 'testing'
  - pytest>=8.3.4 ; extra == 'testing'
  - virtualenv>=20.28.1 ; extra == 'testing'
  - typing-extensions>=4.12.2 ; python_full_version < '3.11' and extra == 'typing'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
  sha256: ec4ebb9444dccfcbff8a2d19b2811b48a20a58dcd08b29e3851cb930fc0f00d8
  md5: 91d4414ab699180b2b0b10b8112c5a2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libasprintf >=0.22.5,<1.0a0
  - libgcc >=13
  - libgettextpo >=0.22.5,<1.0a0
  - mpfr >=4.2.1,<5.0a0
  - readline >=8.2,<9.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 1202471
  timestamp: 1726677363710
- pypi: https://files.pythonhosted.org/packages/84/e8/11ef9ea800061598dd4a2229c408d985c771ee52d6fce7efdc65d3375459/genomicranges-0.6.3-py3-none-any.whl
  name: genomicranges
  version: 0.6.3
  sha256: dedf49c0caa315549cb50df3a1e23cec73b47b76f7567e7b2a1860385431de7c
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - biocframe>=0.6.2
  - iranges>=0.4.2
  - biocutils>=0.2.1
  - numpy
  - joblib ; extra == 'optional'
  - pandas ; extra == 'optional'
  - polars ; extra == 'optional'
  - matplotlib ; extra == 'optional'
  - biobear ; extra == 'optional'
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pandas ; extra == 'testing'
  - polars ; extra == 'testing'
  - matplotlib ; extra == 'testing'
  - joblib ; extra == 'testing'
  - rich ; extra == 'testing'
  - seaborn ; extra == 'testing'
  - biobear ; extra == 'testing'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 460055
  timestamp: 1718980856608
- pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
  name: h11
  version: 0.14.0
  sha256: e3fe4ac4b851c468cc8363d500db52c2ead036020723024a109d37346efaa761
  requires_dist:
  - typing-extensions ; python_full_version < '3.8'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/a5/40/19c0935bf9f25808541a0e3144ac459de696c5b6b6d4511a98d456c69604/hatch-1.14.1-py3-none-any.whl
  name: hatch
  version: 1.14.1
  sha256: 39cdaa59e47ce0c5505d88a951f4324a9c5aafa17e4a877e2fde79b36ab66c21
  requires_dist:
  - click>=8.0.6
  - hatchling>=1.26.3
  - httpx>=0.22.0
  - hyperlink>=21.0.0
  - keyring>=23.5.0
  - packaging>=23.2
  - pexpect~=4.8
  - platformdirs>=2.5.0
  - rich>=11.2.0
  - shellingham>=1.4.0
  - tomli-w>=1.0
  - tomlkit>=0.11.1
  - userpath~=1.7
  - uv>=0.5.23
  - virtualenv>=20.26.6
  - zstandard<1
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/08/e7/ae38d7a6dfba0533684e0b2136817d667588ae3ec984c1a4e5df5eb88482/hatchling-1.27.0-py3-none-any.whl
  name: hatchling
  version: 1.27.0
  sha256: d3a2f3567c4f926ea39849cdf924c7e99e6686c9c8e288ae1037c8fa2a5d937b
  requires_dist:
  - packaging>=24.2
  - pathspec>=0.10.1
  - pluggy>=1.0.0
  - tomli>=1.2.2 ; python_full_version < '3.11'
  - trove-classifiers
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/bioconda/linux-64/htslib-1.21-h566b1c6_1.tar.bz2
  sha256: a2dbd0241e770b168026ed303520eaa6f9f99a4b25cc1fb8a5ec9d8fca10975b
  md5: 944598fba531a668e8fafea92ca39bb4
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libcurl >=8.11.1,<9.0a0
  - libdeflate >=1.22,<1.24.0a0
  - libgcc >=13
  - liblzma >=5.6.3,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.0,<4.0a0
  license: MIT
  license_family: MIT
  size: 3181630
  timestamp: 1734145218913
- pypi: https://files.pythonhosted.org/packages/18/8d/f052b1e336bb2c1fc7ed1aaed898aa570c0b61a09707b108979d9fc6e308/httpcore-1.0.8-py3-none-any.whl
  name: httpcore
  version: 1.0.8
  sha256: 5254cf149bcb5f75e9d1b2b9f729ea4a4b883d1ad7379fc632b727cec23674be
  requires_dist:
  - certifi
  - h11>=0.13,<0.15
  - anyio>=4.0,<5.0 ; extra == 'asyncio'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - trio>=0.22.0,<1.0 ; extra == 'trio'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
  name: httpx
  version: 0.28.1
  sha256: d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad
  requires_dist:
  - anyio
  - certifi
  - httpcore==1.*
  - idna
  - brotli ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - click==8.* ; extra == 'cli'
  - pygments==2.* ; extra == 'cli'
  - rich>=10,<14 ; extra == 'cli'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/6e/aa/8caf6a0a3e62863cbb9dab27135660acba46903b703e224f14f447e57934/hyperlink-21.0.0-py2.py3-none-any.whl
  name: hyperlink
  version: 21.0.0
  sha256: e6b14c37ecb73e89c77d78cdb4c2cc8f3fb59a885c5b3f819ff4ed80f25af1b4
  requires_dist:
  - idna>=2.5
  - typing ; python_full_version < '3.5'
  requires_python: '>=2.6,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*'
- pypi: https://files.pythonhosted.org/packages/9f/cb/18326d2d89ad3b0dd143da971e77afd1e6ca6674f1b1c3df4b6bec6279fc/id-1.5.0-py3-none-any.whl
  name: id
  version: 1.5.0
  sha256: f1434e1cef91f2cbb8a4ec64663d5a23b9ed43ef44c4c957d02583d61714c658
  requires_dist:
  - requests
  - build ; extra == 'dev'
  - bump>=1.3.2 ; extra == 'dev'
  - id[test,lint] ; extra == 'dev'
  - bandit ; extra == 'lint'
  - interrogate ; extra == 'lint'
  - mypy ; extra == 'lint'
  - ruff<0.8.2 ; extra == 'lint'
  - types-requests ; extra == 'lint'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pretend ; extra == 'test'
  - coverage[toml] ; extra == 'test'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
  name: idna
  version: '3.10'
  sha256: 946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3
  requires_dist:
  - ruff>=0.6.2 ; extra == 'all'
  - mypy>=1.11.2 ; extra == 'all'
  - pytest>=8.3.2 ; extra == 'all'
  - flake8>=7.1.1 ; extra == 'all'
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/bioconda/linux-64/infernal-1.1.5-pl5321h7b50bb2_4.tar.bz2
  sha256: 1e62b1fc98a41b7c1c8b13c900e852679e36799dc2ac4f04de1bcfbd1b3a23ef
  md5: 78364081743c5b113116cf25f9a4473a
  depends:
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libgomp
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3904151
  timestamp: 1734085424371
- pypi: https://files.pythonhosted.org/packages/7e/5e/af84054b0ff9f9fbe49a7079d46ba8b4ee7ab6192a0310d4bd2c91254626/install_jdk-1.1.0-py3-none-any.whl
  name: install-jdk
  version: 1.1.0
  sha256: b63f0fcd63f7abab3443d4120ba92716397753b8a8ea3c85762a629925a9936e
  requires_python: '>=3.6,<4.0'
- pypi: https://files.pythonhosted.org/packages/50/fb/396d568039d21344639db96d940d40eb62befe704ef849b27949ded5c3bb/intervaltree-3.1.0.tar.gz
  name: intervaltree
  version: 3.1.0
  sha256: 902b1b88936918f9b2a19e0e5eb7ccb430ae45cde4f39ea4b36932920d33952d
  requires_dist:
  - sortedcontainers>=2.0,<3.0
- pypi: https://files.pythonhosted.org/packages/1b/13/c6e618598d3b8f195fc16cd529bba7184758c050d1d379ba4eafe890c7b6/IRanges-0.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: iranges
  version: 0.4.2
  sha256: ed8d79bbdbedfa68ab8b2e63ea345ec459a00a5b0d2e04fb7e4311976663554c
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - biocutils>=0.2.1
  - biocframe>=0.6.2
  - ncls
  - numpy
  - polars ; extra == 'optional'
  - pandas ; extra == 'optional'
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pandas ; extra == 'testing'
  - polars ; extra == 'testing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/eb/1d/0cda9eb4036f3bc08e315c0f7af3c514a890fa2e89958575555b2de9530d/IRanges-0.4.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: iranges
  version: 0.4.2
  sha256: 34d297582da61ebdec53d353fbf234a0668e3ec9c5a7d9da342d60695115078b
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - biocutils>=0.2.1
  - biocframe>=0.6.2
  - ncls
  - numpy
  - polars ; extra == 'optional'
  - pandas ; extra == 'optional'
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pandas ; extra == 'testing'
  - polars ; extra == 'testing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/7f/66/b15ce62552d84bbfcec9a4873ab79d993a1dd4edb922cbfccae192bd5b5f/jaraco.classes-3.4.0-py3-none-any.whl
  name: jaraco-classes
  version: 3.4.0
  sha256: f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790
  requires_dist:
  - more-itertools
  - sphinx>=3.5 ; extra == 'docs'
  - jaraco-packaging>=9.3 ; extra == 'docs'
  - rst-linker>=1.9 ; extra == 'docs'
  - furo ; extra == 'docs'
  - sphinx-lint ; extra == 'docs'
  - jaraco-tidelift>=1.4 ; extra == 'docs'
  - pytest>=6 ; extra == 'testing'
  - pytest-checkdocs>=2.4 ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-mypy ; extra == 'testing'
  - pytest-enabler>=2.2 ; extra == 'testing'
  - pytest-ruff>=0.2.1 ; extra == 'testing'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/ff/db/0c52c4cf5e4bd9f5d7135ec7669a3a767af21b3a308e1ed3674881e52b62/jaraco.context-6.0.1-py3-none-any.whl
  name: jaraco-context
  version: 6.0.1
  sha256: f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4
  requires_dist:
  - backports-tarfile ; python_full_version < '3.12'
  - sphinx>=3.5 ; extra == 'doc'
  - jaraco-packaging>=9.3 ; extra == 'doc'
  - rst-linker>=1.9 ; extra == 'doc'
  - furo ; extra == 'doc'
  - sphinx-lint ; extra == 'doc'
  - jaraco-tidelift>=1.4 ; extra == 'doc'
  - pytest>=6,!=8.1.* ; extra == 'test'
  - pytest-checkdocs>=2.4 ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-mypy ; extra == 'test'
  - pytest-enabler>=2.2 ; extra == 'test'
  - portend ; extra == 'test'
  - pytest-ruff>=0.2.1 ; sys_platform != 'cygwin' and extra == 'test'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/9f/4f/24b319316142c44283d7540e76c7b5a6dbd5db623abd86bb7b3491c21018/jaraco.functools-4.1.0-py3-none-any.whl
  name: jaraco-functools
  version: 4.1.0
  sha256: ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649
  requires_dist:
  - more-itertools
  - pytest-checkdocs>=2.4 ; extra == 'check'
  - pytest-ruff>=0.2.1 ; sys_platform != 'cygwin' and extra == 'check'
  - pytest-cov ; extra == 'cover'
  - sphinx>=3.5 ; extra == 'doc'
  - jaraco-packaging>=9.3 ; extra == 'doc'
  - rst-linker>=1.9 ; extra == 'doc'
  - furo ; extra == 'doc'
  - sphinx-lint ; extra == 'doc'
  - jaraco-tidelift>=1.4 ; extra == 'doc'
  - pytest-enabler>=2.2 ; extra == 'enabler'
  - pytest>=6,!=8.1.* ; extra == 'test'
  - jaraco-classes ; extra == 'test'
  - pytest-mypy ; extra == 'type'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b2/a3/e137168c9c44d18eff0376253da9f1e9234d0239e0ee230d2fee6cea8e55/jeepney-0.9.0-py3-none-any.whl
  name: jeepney
  version: 0.9.0
  sha256: 97e5714520c16fc0a45695e5365a2e11b81ea79bba796e26f9f1d178cb182683
  requires_dist:
  - pytest ; extra == 'test'
  - pytest-trio ; extra == 'test'
  - pytest-asyncio>=0.17 ; extra == 'test'
  - testpath ; extra == 'test'
  - trio ; extra == 'test'
  - async-timeout ; python_full_version < '3.11' and extra == 'test'
  - trio ; extra == 'trio'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/d3/32/da7f44bcb1105d3e88a0b74ebdca50c59121d2ddf71c9e34ba47df7f3a56/keyring-25.6.0-py3-none-any.whl
  name: keyring
  version: 25.6.0
  sha256: 552a3f7af126ece7ed5c89753650eec89c7eaae8617d0aa4d9ad2b75111266bd
  requires_dist:
  - pywin32-ctypes>=0.2.0 ; sys_platform == 'win32'
  - secretstorage>=3.2 ; sys_platform == 'linux'
  - jeepney>=0.4.2 ; sys_platform == 'linux'
  - importlib-metadata>=4.11.4 ; python_full_version < '3.12'
  - jaraco-classes
  - importlib-resources ; python_full_version < '3.9'
  - jaraco-functools
  - jaraco-context
  - pytest>=6,!=8.1.* ; extra == 'test'
  - pyfakefs ; extra == 'test'
  - sphinx>=3.5 ; extra == 'doc'
  - jaraco-packaging>=9.3 ; extra == 'doc'
  - rst-linker>=1.9 ; extra == 'doc'
  - furo ; extra == 'doc'
  - sphinx-lint ; extra == 'doc'
  - jaraco-tidelift>=1.4 ; extra == 'doc'
  - pytest-checkdocs>=2.4 ; extra == 'check'
  - pytest-ruff>=0.2.1 ; sys_platform != 'cygwin' and extra == 'check'
  - pytest-cov ; extra == 'cover'
  - pytest-enabler>=2.2 ; extra == 'enabler'
  - pytest-mypy ; extra == 'type'
  - pygobject-stubs ; extra == 'type'
  - shtab ; extra == 'type'
  - types-pywin32 ; extra == 'type'
  - shtab>=1.1.0 ; extra == 'completion'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  purls: []
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
  sha256: db73f38155d901a610b2320525b9dd3b31e4949215c870685fd92ea61b5ce472
  md5: 01f8d123c96816249efd255a31ad7712
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 671240
  timestamp: 1740155456116
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.23.1-h8e693c7_0.conda
  sha256: 13b863584fccbb9089de73a2442e540703ce4873e4719c9d98c98e4a8e12f9d1
  md5: 988f4937281a66ca19d1adb3b5e3f859
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 43179
  timestamp: 1739038705987
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.13.0-h332b0f4_0.conda
  sha256: 38e528acfaa0276b7052f4de44271ff9293fdb84579650601a8c49dac171482a
  md5: cbdc92ac0d93fe3c796e36ad65c7905c
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.1,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  purls: []
  size: 438088
  timestamp: 1743601695669
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.23-h4ddbbb0_0.conda
  sha256: 511d801626d02f4247a04fff957cc6e9ec4cc7e8622bd9acd076bcdc5de5fe66
  md5: 8dfae1d2e74767e9ce36d5fa0d8605db
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 72255
  timestamp: 1734373823254
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h767d61c_2.conda
  sha256: 3a572d031cb86deb541d15c1875aaa097baefc0c580b54dc61f5edab99215792
  md5: ef504d1acbd74b7cc6849ef8af47dd03
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 14.2.0 h767d61c_2
  - libgcc-ng ==14.2.0=*_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 847885
  timestamp: 1740240653082
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_2.conda
  sha256: fb7558c328b38b2f9d2e412c48da7890e7721ba018d733ebdfea57280df01904
  md5: a2222a6ada71fb478682efe483ce0f92
  depends:
  - libgcc 14.2.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 53758
  timestamp: 1740240660904
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.23.1-h5888daf_0.conda
  sha256: 190097140d9c16637aa516757d8087f17e8c22cc844c87288da64404b81ef43c
  md5: a09ce5decdef385bcce78c32809fa794
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 166867
  timestamp: 1739038720211
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h767d61c_2.conda
  sha256: 1a3130e0b9267e781b89399580f3163632d59fe5b0142900d63052ab1a53490e
  md5: 06d02030237f4d5b3d9a7e7d348fe3c6
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 459862
  timestamp: 1740240588123
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_0.conda
  sha256: f4f21dfc54b08d462f707b771ecce3fa9bc702a2a05b55654f64154f48b141ef
  md5: 0e87378639676987af32fee53ba32258
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: 0BSD
  purls: []
  size: 112709
  timestamp: 1743771086123
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33408
  timestamp: 1697359010159
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.1-hee588c1_2.conda
  sha256: a086289bf75c33adc1daed3f1422024504ffb5c3c8b3285c49f025c29708ed16
  md5: 962d6ac93c30b1dfc54c9cccafd1003e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 918664
  timestamp: 1742083674731
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hf672d98_0.conda
  sha256: 0407ac9fda2bb67e11e357066eff144c845801d00b5f664efbc48813af1e7bb9
  md5: be2de152d8073ef1c01b7728475f2fe7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 304278
  timestamp: 1732349402869
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-h8f9b012_2.conda
  sha256: 8f5bd92e4a24e1d35ba015c5252e8f818898478cb3bc50bd8b12ab54707dc4da
  md5: a78c856b6dc6bf4ea8daeb9beaaa3fb0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 14.2.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3884556
  timestamp: 1740240685253
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-14.2.0-h4852527_2.conda
  sha256: e86f38b007cf97cc2c67cd519f2de12a313c4ee3f5ef11652ad08932a5e34189
  md5: c75da67f045c2627f59e6fcb5f4e3a9b
  depends:
  - libstdcxx 14.2.0 h8f9b012_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 53830
  timestamp: 1740240722530
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.7-h81593ed_1.conda
  sha256: c4f59563e017eba378ea843be5ebde4b0546c72bbe4c1e43b2b384379e827635
  md5: 0619e8fc4c8025a908ea3a3422d3b775
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - icu <0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 691042
  timestamp: 1743794600936
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- pypi: https://files.pythonhosted.org/packages/97/0d/f74b301ea0c44068274a0c95815e1a183a0c9af5ea32db817d8b3d33af36/lightmotif-0.9.1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: lightmotif
  version: 0.9.1
  sha256: 12dedfc6268be2a1b6d069c9d066b0fc0dd436c2ecfa90331516b5ab8f6566d6
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/ef/c1/d126e62bdf8f7f9ad84660773fb2eab2f9aef9df71d9fd47f1466f47f54e/lightmotif-0.9.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: lightmotif
  version: 0.9.1
  sha256: f823f056958f8d37ff601e21303d6487d1026f872d134aca268ecb22dc3be89b
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/bioconda/linux-64/linearfold-1.0.1.dev20220829-h9948957_1.tar.bz2
  sha256: 595f91028bb56203ed5767459dfaadd34765caa3253315dc38afedfe49cb94dc
  md5: 34e2e2f84ac974c16307ce03089be1a4
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3,<4
  - python-gflags
  license: custom
  purls: []
  size: 148881
  timestamp: 1734149130305
- pypi: https://files.pythonhosted.org/packages/90/48/9818341371f20a2644267a1769aaecb7642f74839192afad278a38e0fe14/mappy-2.28.tar.gz
  name: mappy
  version: '2.28'
  sha256: 0ebf7a5d62bd668f5456028215e26176e180ca68161ac18d4f7b48045484cebb
- pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
  name: markdown-it-py
  version: 3.0.0
  sha256: 355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1
  requires_dist:
  - mdurl~=0.1
  - psutil ; extra == 'benchmarking'
  - pytest ; extra == 'benchmarking'
  - pytest-benchmark ; extra == 'benchmarking'
  - pre-commit~=3.0 ; extra == 'code-style'
  - commonmark~=0.9 ; extra == 'compare'
  - markdown~=3.4 ; extra == 'compare'
  - mistletoe~=1.0 ; extra == 'compare'
  - mistune~=2.0 ; extra == 'compare'
  - panflute~=2.3 ; extra == 'compare'
  - linkify-it-py>=1,<3 ; extra == 'linkify'
  - mdit-py-plugins ; extra == 'plugins'
  - gprof2dot ; extra == 'profiling'
  - mdit-py-plugins ; extra == 'rtd'
  - myst-parser ; extra == 'rtd'
  - pyyaml ; extra == 'rtd'
  - sphinx ; extra == 'rtd'
  - sphinx-copybutton ; extra == 'rtd'
  - sphinx-design ; extra == 'rtd'
  - sphinx-book-theme ; extra == 'rtd'
  - jupyter-sphinx ; extra == 'rtd'
  - coverage ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-regressions ; extra == 'testing'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
  name: mdurl
  version: 0.1.2
  sha256: 84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/bioconda/linux-64/megahit-1.2.9-h5ca1c30_6.tar.bz2
  sha256: 3af8adbb58dc73ff4eec69b17178145c547338d3c1fddfd3efbc7ba4307786de
  md5: 55b229a859441b4c7de743280f492975
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python
  - zlib
  license: GPL-3.0
  license_family: GPL
  purls: []
  size: 3112123
  timestamp: 1736253690659
- conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
  sha256: f64af29a82a8642138b34cf7b033b6344c0bd24a6bba898cf653edde6e0f727c
  md5: 561fb589d37cff61ec6b887fc2976498
  depends:
  - _openmp_mutex >=4.5
  - aria2
  - bzip2 >=1.0.8,<2.0a0
  - gawk
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  purls: []
  size: 117468424
  timestamp: 1737229717596
- pypi: https://files.pythonhosted.org/packages/23/62/0fe302c6d1be1c777cab0616e6302478251dfbf9055ad426f5d0def75c89/more_itertools-10.6.0-py3-none-any.whl
  name: more-itertools
  version: 10.6.0
  sha256: 6eb054cb4b6db1473f6e15fcc676a08e4732548acd47c708f0e179c2c7c01e89
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  purls: []
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncbi-datasets-cli-17.3.0-ha770c72_1.conda
  sha256: 2927eb9dfa6ad5f71bba67a86eea2e1fe32cf62664913885596db4ab3d69d762
  md5: 6a7857392cb625ea0ed04d55ed045ba9
  depends:
  - ca-certificates
  license: Public Domain
  purls: []
  size: 14321161
  timestamp: 1743704228224
- pypi: https://files.pythonhosted.org/packages/85/86/10fbefe30a47c36d041966d9ddbb208fcfd15cf02bc2e57abac9fab186cd/ncls-0.0.68-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: ncls
  version: 0.0.68
  sha256: 906af9e789412ef6d0a8b08d6870b40d39304865973f550b0eb6994d3d3217e8
  requires_dist:
  - numpy
  - black ; extra == 'dev'
  - bumpver ; extra == 'dev'
  - isort ; extra == 'dev'
  - pip-tools ; extra == 'dev'
  - pytest ; extra == 'dev'
- pypi: https://files.pythonhosted.org/packages/88/b8/210d5cb1fa85c7675323aacbd52af11553dc190aad1c15584699f40797f1/ncls-0.0.68.tar.gz
  name: ncls
  version: 0.0.68
  sha256: 81aaa5abb123bb21797ed2f8ef921e20222db14a3ecbc61ccf447532f2b7ba93
  requires_dist:
  - numpy
  - black ; extra == 'dev'
  - bumpver ; extra == 'dev'
  - isort ; extra == 'dev'
  - pip-tools ; extra == 'dev'
  - pytest ; extra == 'dev'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- pypi: https://files.pythonhosted.org/packages/33/83/5f50f1dac42953e64588be292904fcaede10439323b0a859e1caf9b43ab1/needletail-0.6.3.tar.gz
  name: needletail
  version: 0.6.3
  sha256: 93da7faa511eae7da4a3186c0d7d73ccfb3b4f14d9f9777fac20dd2bd75efb61
- pypi: https://files.pythonhosted.org/packages/88/03/7c6d78a3a84344c885236401e705280bf29b664215b3a8b1e36f9221036d/needletail-0.6.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: needletail
  version: 0.6.3
  sha256: de7f60c7b61cb25c52c2bfa516cc146487a31f53702d8429ca46edb813d5cc4f
- pypi: https://files.pythonhosted.org/packages/11/a9/1cd3c6964ec51daed7b01ca4686a5c793581bf4492cbd7274b3f544c9abe/nh3-0.2.21-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: nh3
  version: 0.2.21
  sha256: a5f77e62aed5c4acad635239ac1290404c7e940c81abe561fd2af011ff59f585
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b9/14/78635daab4b07c0930c919d451b8bf8c164774e6a3413aed04a6d95758ce/numpy-2.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: numpy
  version: 2.0.2
  sha256: f26b258c385842546006213344c50655ff1555a9338e2e5e02a0756dc3e803dd
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/02/e2/e2cbb8d634151aab9528ef7b8bab52ee4ab10e076509285602c2a3a686e0/numpy-2.2.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: numpy
  version: 2.2.4
  sha256: 4f92084defa704deadd4e0a5ab1dc52d8ac9e8a8ef617f3fbb853e79b0ea3592
  requires_python: '>=3.10'
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_0.conda
  sha256: 38285d280f84f1755b7c54baf17eccf2e3e696287954ce0adca16546b85ee62c
  md5: bb539841f2a3fde210f387d00ed4bb9d
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3121673
  timestamp: 1744132167438
- pypi: https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl
  name: packaging
  version: '24.2'
  sha256: 09abb1bccd265c01f4a3aa3f7a7db064b36514d2cba19a2f694fe6150451a759
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
  name: pathspec
  version: 0.12.1
  sha256: a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
  build_number: 7
  sha256: 9ec32b6936b0e37bcb0ed34f22ec3116e75b3c0964f9f50ecea5f58734ed6ce9
  md5: f2cfec9406850991f4e3d960cc9e3321
  depends:
  - libgcc-ng >=12
  - libxcrypt >=4.4.36
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  purls: []
  size: 13344463
  timestamp: 1703310653947
- pypi: https://files.pythonhosted.org/packages/9e/c3/059298687310d527a58bb01f3b1965787ee3b40dce76752eda8b44e9a2c5/pexpect-4.9.0-py2.py3-none-any.whl
  name: pexpect
  version: 4.9.0
  sha256: 7236d1e080e4936be2dc3e326cec0af72acf9212a7e1d060210e70a47e253523
  requires_dist:
  - ptyprocess>=0.5
- conda: https://conda.anaconda.org/conda-forge/linux-64/pigz-2.8-hadc24fc_1.conda
  sha256: 79f863f3989a13bfc7901642caf8db5d9f6740a48804145aab51d2ce0ca3c6b9
  md5: b7f0995c7c10a80015668a62f2b608e7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Zlib
  purls: []
  size: 72378
  timestamp: 1738906682885
- conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.0.1-pyh8b19718_0.conda
  sha256: 585940f09d87787f79f73ff5dff8eb2af8a67e5bec5eebf2f553cd26c840ba69
  md5: 79b5c1440aedc5010f687048d9103628
  depends:
  - python >=3.9,<3.13.0a0
  - setuptools
  - wheel
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pip?source=hash-mapping
  size: 1256460
  timestamp: 1739142857253
- conda: https://conda.anaconda.org/bioconda/linux-64/plass-5.cf8933-pl5321h6a68c12_1.tar.bz2
  sha256: 8ac5f900cabd64e0e4b514931c8db8a8139c64d07a78004ec0d0674b41e53cc2
  md5: 0557a6a9d201d1bc8089755c6abdad73
  depends:
  - _openmp_mutex >=4.5
  - bzip2 >=1.0.8,<2.0a0
  - gawk
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<2.0a0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - zlib
  license: GPLv3
  purls: []
  size: 4665015
  timestamp: 1720674168928
- pypi: https://files.pythonhosted.org/packages/6d/45/59578566b3275b8fd9157885918fcd0c4d74162928a5310926887b856a51/platformdirs-4.3.7-py3-none-any.whl
  name: platformdirs
  version: 4.3.7
  sha256: a03875334331946f13c549dbd8f4bac7a13a50a895a0eb1e8c6a8ace80d40a94
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - proselint>=0.14 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - appdirs==1.4.4 ; extra == 'test'
  - covdefaults>=2.3 ; extra == 'test'
  - pytest-cov>=6 ; extra == 'test'
  - pytest-mock>=3.14 ; extra == 'test'
  - pytest>=8.3.4 ; extra == 'test'
  - mypy>=1.14.1 ; extra == 'type'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/88/5f/e351af9a41f866ac3f1fac4ca0613908d9a41741cfcf2228f4ad853b697d/pluggy-1.5.0-py3-none-any.whl
  name: pluggy
  version: 1.5.0
  sha256: 44e1ad92c8ca002de6377e165f3e0f1be63266ab4d554740532335b9d75ea669
  requires_dist:
  - pre-commit ; extra == 'dev'
  - tox ; extra == 'dev'
  - pytest ; extra == 'testing'
  - pytest-benchmark ; extra == 'testing'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/20/c1/c65924c0ca186f481c02b531f1ec66c34f9bbecc11d70246562bb4949876/polars-1.27.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: polars
  version: 1.27.1
  sha256: f801e0d9da198eb97cfb4e8af4242b8396878ff67b655c71570b7e333102b72b
  requires_dist:
  - polars-cloud>=0.0.1a1 ; extra == 'polars-cloud'
  - numpy>=1.16.0 ; extra == 'numpy'
  - pandas ; extra == 'pandas'
  - polars[pyarrow] ; extra == 'pandas'
  - pyarrow>=7.0.0 ; extra == 'pyarrow'
  - pydantic ; extra == 'pydantic'
  - fastexcel>=0.9 ; extra == 'calamine'
  - openpyxl>=3.0.0 ; extra == 'openpyxl'
  - xlsx2csv>=0.8.0 ; extra == 'xlsx2csv'
  - xlsxwriter ; extra == 'xlsxwriter'
  - polars[calamine,openpyxl,xlsx2csv,xlsxwriter] ; extra == 'excel'
  - adbc-driver-manager[dbapi] ; extra == 'adbc'
  - adbc-driver-sqlite[dbapi] ; extra == 'adbc'
  - connectorx>=0.3.2 ; extra == 'connectorx'
  - sqlalchemy ; extra == 'sqlalchemy'
  - polars[pandas] ; extra == 'sqlalchemy'
  - polars[adbc,connectorx,sqlalchemy] ; extra == 'database'
  - fsspec ; extra == 'fsspec'
  - deltalake>=0.19.0 ; extra == 'deltalake'
  - pyiceberg>=0.7.1 ; extra == 'iceberg'
  - gevent ; extra == 'async'
  - cloudpickle ; extra == 'cloudpickle'
  - matplotlib ; extra == 'graph'
  - altair>=5.4.0 ; extra == 'plot'
  - great-tables>=0.8.0 ; extra == 'style'
  - tzdata ; sys_platform == 'win32' and extra == 'timezone'
  - cudf-polars-cu12 ; extra == 'gpu'
  - polars[async,cloudpickle,database,deltalake,excel,fsspec,graph,iceberg,numpy,pandas,plot,pyarrow,pydantic,style,timezone] ; extra == 'all'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/9c/39/0f88a830a1c8a3aba27fededc642da37613c57cbff143412e3536f89784f/psutil-6.1.1-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: psutil
  version: 6.1.1
  sha256: 97f7cb9921fbec4904f522d972f0c0e1f4fabbdd4e0287813b21215074a0f160
  requires_dist:
  - abi3audit ; extra == 'dev'
  - black ; extra == 'dev'
  - check-manifest ; extra == 'dev'
  - coverage ; extra == 'dev'
  - packaging ; extra == 'dev'
  - pylint ; extra == 'dev'
  - pyperf ; extra == 'dev'
  - pypinfo ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - requests ; extra == 'dev'
  - rstcheck ; extra == 'dev'
  - ruff ; extra == 'dev'
  - sphinx ; extra == 'dev'
  - sphinx-rtd-theme ; extra == 'dev'
  - toml-sort ; extra == 'dev'
  - twine ; extra == 'dev'
  - virtualenv ; extra == 'dev'
  - vulture ; extra == 'dev'
  - wheel ; extra == 'dev'
  - pytest ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - setuptools ; extra == 'test'
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*'
- pypi: https://files.pythonhosted.org/packages/22/a6/858897256d0deac81a172289110f31629fc4cee19b6f01283303e18c8db3/ptyprocess-0.7.0-py2.py3-none-any.whl
  name: ptyprocess
  version: 0.7.0
  sha256: 4b41f3967fce3af57cc7e94b888626c18bf37a083e3651ca8feeb66d492fef35
- pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
  name: pycparser
  version: '2.22'
  sha256: c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/1f/9b/860b1f95ed305c0ae42d943cf9006733fd6bbb879ccf21a542e905b5d991/pyfastx-2.2.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyfastx
  version: 2.2.0
  sha256: 72e347e959de5933e57fabe348b14ce5f1ccdef6d6064a7339a537e7c9994f43
- pypi: https://files.pythonhosted.org/packages/58/61/56b32f2825759f3f0c796567fbe89c3f8462a6db7105ec30b30f2a96b88e/pyfastx-2.2.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyfastx
  version: 2.2.0
  sha256: a15ea288cbd69f0553f3ea1df48d9098fda32b19b96887e68d85eb97eddce889
- pypi: https://files.pythonhosted.org/packages/8a/0b/9fcc47d19c48b59121088dd6da2488a49d5f72dacf8262e2790a1d2c7d15/pygments-2.19.1-py3-none-any.whl
  name: pygments
  version: 2.19.1
  sha256: 9ea1544ad55cecf4b8242fab6dd35a93bbce657034b0611ee383099054ab6d8c
  requires_dist:
  - colorama>=0.4.6 ; extra == 'windows-terminal'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/4f/00/029fbc534fdd30f074acd7812e15f8974929459e55dffb312677eafd0c67/pyhmmer-0.11.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyhmmer
  version: 0.11.0
  sha256: 8e66a6c2a8defa9ceb4aacfe099de6db00b7a8c08135aaad276d308eeaed2794
  requires_dist:
  - psutil~=6.0
  - importlib-resources ; python_full_version < '3.9' and extra == 'test'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/76/3a/78b667b16d55fa50cb2130a8c68c810bfa56ac3293a39c4bbea829bbc3b5/pyhmmer-0.11.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyhmmer
  version: 0.11.0
  sha256: d1030af6576015339b5f7cb9d8c1b8055d736c7cd011faf37bb589dd79f7cc9b
  requires_dist:
  - psutil~=6.0
  - importlib-resources ; python_full_version < '3.9' and extra == 'test'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/19/d1/853ea09b04a48bc74005592bbcd284dfafc717c477d71a145535b293dddb/pyrodigal-3.6.3.post1-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyrodigal
  version: 3.6.3.post1
  sha256: 06d6a3d3cd9f1268c0fcdd655d7af3caa21c51db2823c8c5edd1f9d072319a98
  requires_dist:
  - archspec~=0.2.0
  - importlib-resources ; python_full_version < '3.9' and extra == 'test'
  - isal~=1.1 ; extra == 'isal'
  - lz4~=4.0 ; extra == 'lz4'
  - zstandard~=0.22 ; extra == 'zst'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/aa/9b/060c0269ff913b2d5c48e22a684a73534a0dfcd9a46d21d450987263d962/pyrodigal-3.6.3.post1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyrodigal
  version: 3.6.3.post1
  sha256: 8c5e5038e6a33ed84e4894957fe2c117235e20483409ea7788eb689fbd33f099
  requires_dist:
  - archspec~=0.2.0
  - importlib-resources ; python_full_version < '3.9' and extra == 'test'
  - isal~=1.1 ; extra == 'isal'
  - lz4~=4.0 ; extra == 'lz4'
  - zstandard~=0.22 ; extra == 'zst'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/d6/8f/e78b92bc836a7b50a3497ee655983561dec4ac2df06136f0c55e59a00144/pyrodigal_gv-0.3.2-py2.py3-none-any.whl
  name: pyrodigal-gv
  version: 0.3.2
  sha256: cd6d922d7495ff853a7385ef8abe68498113c177a5ded3d07f93c28b8c990120
  requires_dist:
  - pyrodigal!=3.5.0,~=3.5
  - importlib-resources ; python_full_version < '3.9'
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.10-h9e4cc4f_0_cpython.conda
  sha256: 4dc1da115805bd353bded6ab20ff642b6a15fcc72ac2f3de0e1d014ff3612221
  md5: a41d26cd4d47092d683915d058380dec
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.49.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  purls: []
  size: 31279179
  timestamp: 1744325164633
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.9.22-h85ef794_1_cpython.conda
  build_number: 1
  sha256: d55739a308bd343ebe1990562a4ea8c440d246779f6da9b291068ec116699b64
  md5: b23565542b4974e9fe3e81bdfd8799c3
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.49.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.9.* *_cp39
  license: Python-2.0
  purls: []
  size: 23620589
  timestamp: 1744674892969
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gflags-3.1.2-py_0.tar.bz2
  sha256: ce8692db9d15645c502348486f37963288e435bfeb468906662f992572c4a91e
  md5: 9a6bf8f8bf1fba16dfeb6c3076605c5f
  depends:
  - python
  - six
  license: BSD 3-Clause
  purls:
  - pkg:pypi/python-gflags?source=hash-mapping
  size: 41758
  timestamp: 1531724278947
- conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.12-6_cp312.conda
  build_number: 6
  sha256: 09aff7ca31d1dbee63a504dba89aefa079b7c13a50dae18e1fe40a40ea71063e
  md5: 95bd67b1113859774c30418e8481f9d8
  constrains:
  - python 3.12.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6872
  timestamp: 1743483197238
- conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.9-6_cp39.conda
  build_number: 6
  sha256: 89cd5b4af38e3fe0d7ff0fd78a4fd4092d99bcca982a84521ff38d0eb1f882a3
  md5: d36fa4e4fa54ca1200cd7cf11abd3942
  constrains:
  - python 3.9.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6833
  timestamp: 1743483174690
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- pypi: https://files.pythonhosted.org/packages/e1/67/921ec3024056483db83953ae8e48079ad62b92db7880013ca77632921dd0/readme_renderer-44.0-py3-none-any.whl
  name: readme-renderer
  version: '44.0'
  sha256: 2fbca89b81a08526aadf1357a8c2ae889ec05fb03f5da67f9769c9a592166151
  requires_dist:
  - nh3>=0.2.14
  - docutils>=0.21.2
  - pygments>=2.5.1
  - cmarkgfm>=0.8.0 ; extra == 'md'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
  name: requests
  version: 2.32.3
  sha256: 70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6
  requires_dist:
  - charset-normalizer>=2,<4
  - idna>=2.5,<4
  - urllib3>=1.21.1,<3
  - certifi>=2017.4.17
  - pysocks>=1.5.6,!=1.5.7 ; extra == 'socks'
  - chardet>=3.0.2,<6 ; extra == 'use-chardet-on-py3'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl
  name: requests-toolbelt
  version: 1.0.0
  sha256: cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06
  requires_dist:
  - requests>=2.0.1,<3.0.0
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*'
- pypi: https://files.pythonhosted.org/packages/ff/9a/9afaade874b2fa6c752c36f1548f718b5b83af81ed9b76628329dab81c1b/rfc3986-2.0.0-py2.py3-none-any.whl
  name: rfc3986
  version: 2.0.0
  sha256: 50b1502b60e289cb37883f3dfd34532b8873c7de9f49bb546641ce9cbd256ebd
  requires_dist:
  - idna ; extra == 'idna2008'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
  name: rich
  version: 14.0.0
  sha256: 1c9491e1951aac09caffd42f448ee3d04e58923ffe14993f6e83068dc395d7e0
  requires_dist:
  - ipywidgets>=7.5.1,<9 ; extra == 'jupyter'
  - markdown-it-py>=2.2.0
  - pygments>=2.13.0,<3.0.0
  - typing-extensions>=4.0.0,<5.0 ; python_full_version < '3.11'
  requires_python: '>=3.8.0'
- pypi: https://files.pythonhosted.org/packages/fa/69/963f0bf44a654f6465bdb66fb5a91051b0d7af9f742b5bd7202607165036/rich_click-1.8.8-py3-none-any.whl
  name: rich-click
  version: 1.8.8
  sha256: 205aabd5a98e64ab2c105dee9e368be27480ba004c7dfa2accd0ed44f9f1550e
  requires_dist:
  - click>=7
  - importlib-metadata ; python_full_version < '3.8'
  - rich>=10.7
  - typing-extensions>=4
  - mypy ; extra == 'dev'
  - packaging ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - pytest ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - rich-codex ; extra == 'dev'
  - ruff ; extra == 'dev'
  - types-setuptools ; extra == 'dev'
  - markdown-include ; extra == 'docs'
  - mkdocs ; extra == 'docs'
  - mkdocs-glightbox ; extra == 'docs'
  - mkdocs-material[imaging]~=9.5.18 ; extra == 'docs'
  - mkdocs-material-extensions ; extra == 'docs'
  - mkdocs-rss-plugin ; extra == 'docs'
  - mkdocstrings[python] ; extra == 'docs'
  - rich-codex ; extra == 'docs'
  requires_python: '>=3.7'
- pypi: .
  name: rolypoly-bio
  version: 0.6.11
  sha256: 6d24d316dcc9504ea7fa95ac53dd0a310110f09dd24ebabc31d9a37f47d8a4af
  requires_dist:
  - bbmapy>=0.0.46,<0.0.48
  - genomicranges>=0.6.3,<0.7
  - intervaltree>=3.1.0,<4
  - iranges>=0.4.2,<0.5
  - lightmotif>=0.9.1,<0.10
  - mappy>=2.28,<3
  - needletail>=0.6.3,<0.8
  - polars>=1.26.0,<2
  - psutil>=6.1.1,<7
  - pyfastx>=2.2.0,<3
  - pyhmmer>=0.11.0,<0.12
  - pyrodigal-gv>=0.3.2,<0.4
  - requests>=2.32.3,<3
  - rich-click>=1.8.8,<2
  - rich>=14.0.0,<15
  - viennarna>=2.7.0,<3
  requires_python: '>=3.9'
  editable: true
- pypi: https://files.pythonhosted.org/packages/61/9f/a3e34de425a668284e7024ee6fd41f452f6fa9d817f1f3495b46e5e3a407/ruff-0.11.6-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: ruff
  version: 0.11.6
  sha256: 15adac20ef2ca296dd3d8e2bedc6202ea6de81c091a74661c3666e5c4c223ff6
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/54/24/b4293291fa1dd830f353d2cb163295742fa87f179fcc8a20a306a81978b7/SecretStorage-3.3.3-py3-none-any.whl
  name: secretstorage
  version: 3.3.3
  sha256: f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99
  requires_dist:
  - cryptography>=2.0
  - jeepney>=0.6
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/bioconda/linux-64/seqkit-2.10.0-h9ee0642_0.tar.bz2
  sha256: 878012ec7adf52a41c48b7e08c35f1b0b2b43de8012f3530ffc52b106ab02b53
  md5: 81a69f6cb9d20aa9391af6263a3f5f91
  license: MIT
  purls: []
  size: 7760865
  timestamp: 1741772543573
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-78.1.0-pyhff2d567_0.conda
  sha256: d4c74d2140f2fbc72fe5320cbd65f3fd1d1f7832ab4d7825c37c38ab82440ae2
  md5: a42da9837e46c53494df0044c3eb1f53
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools?source=compressed-mapping
  size: 786557
  timestamp: 1743775941985
- pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
  name: shellingham
  version: 1.5.4
  sha256: 7ecfff8f2fd72616f7481040475a65b2bf8af90a56c89140852d1120324e8686
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/six?source=hash-mapping
  size: 16385
  timestamp: 1733381032766
- pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
  name: sniffio
  version: 1.3.1
  sha256: 2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/32/46/9cb0e58b2deb7f82b84065f37f3bffeb12413f947f9388e4cac22c4621ce/sortedcontainers-2.4.0-py2.py3-none-any.whl
  name: sortedcontainers
  version: 2.4.0
  sha256: a163dcaede0f1c021485e957a39245190e74249897e2ae4b2aa38595db237ee0
- conda: https://conda.anaconda.org/bioconda/linux-64/spades-4.1.0-haf24da9_0.tar.bz2
  sha256: 3016af504c07e8d9390dd0176a84b3ff041e112f50d394274fcca92c594c2ca7
  md5: b6172fef21d2ac686b734d134ed308d3
  depends:
  - _openmp_mutex >=4.5
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libgomp
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.8
  license: GPL-2.0-only
  license_family: GPL
  purls: []
  size: 10971689
  timestamp: 1740242240905
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
  sha256: b2819dd77faee0ea1f14774b603db33da44c14f7662982d4da4bbe76ac8a8976
  md5: f0afd0c7509f6c1b8d77ee64d7ba64b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 179639
  timestamp: 1743578685131
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3318875
  timestamp: 1699202167581
- pypi: https://files.pythonhosted.org/packages/c7/18/c86eb8e0202e32dd3df50d43d7ff9854f8e0603945ff398974c1d91ac1ef/tomli_w-1.2.0-py3-none-any.whl
  name: tomli-w
  version: 1.2.0
  sha256: 188306098d013b691fcadc011abd66727d3c414c571bb01b1a174ba8c983cf90
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/f9/b6/a447b5e4ec71e13871be01ba81f5dfc9d0af7e473da256ff46bc0e24026f/tomlkit-0.13.2-py3-none-any.whl
  name: tomlkit
  version: 0.13.2
  sha256: 7a974427f6e119197f670fbbbeae7bef749a6c14e793db934baefc1b5f03efde
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/bioconda/linux-64/trnascan-se-2.0.12-pl5321h7b50bb2_2.tar.bz2
  sha256: 7e32f5727dfbf989466d8bc285215ab32d6f675d830ff2988b0a82a4d2f440f4
  md5: 785199e6cbd7260af9203c22ddbdacab
  depends:
  - infernal >=1.1.4
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-3.0-or-later
  license_family: GPL3
  purls: []
  size: 3271182
  timestamp: 1734214903046
- pypi: https://files.pythonhosted.org/packages/70/7d/a2271b98b833680561ab3fcd60ab682478dc4f7cc023fab24991601ac8ac/trove_classifiers-2025.4.11.15-py3-none-any.whl
  name: trove-classifiers
  version: 2025.4.11.15
  sha256: e7d98983f004df35293caf954bdfe944b139eb402677a97115450e320f0bd855
- pypi: https://files.pythonhosted.org/packages/7c/b6/74e927715a285743351233f33ea3c684528a0d374d2e43ff9ce9585b73fe/twine-6.1.0-py3-none-any.whl
  name: twine
  version: 6.1.0
  sha256: a47f973caf122930bf0fbbf17f80b83bc1602c9ce393c7845f289a3001dc5384
  requires_dist:
  - readme-renderer>=35.0
  - requests>=2.20
  - requests-toolbelt>=0.8.0,!=0.9.0
  - urllib3>=1.26.0
  - importlib-metadata>=3.6 ; python_full_version < '3.10'
  - keyring>=15.1 ; platform_machine != 'ppc64le' and platform_machine != 's390x'
  - rfc3986>=1.4.0
  - rich>=12.0.0
  - packaging>=24.0
  - id
  - keyring>=15.1 ; extra == 'keyring'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/8b/54/b1ae86c0973cc6f0210b53d508ca3641fb6d0c56823f288d108bc7ab3cc8/typing_extensions-4.13.2-py3-none-any.whl
  name: typing-extensions
  version: 4.13.2
  sha256: a439e7c04b49fec3e5d3e2beaa21755cadbbdc391694e28ccdd36ca4a1408f8c
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- pypi: https://files.pythonhosted.org/packages/6b/11/cc635220681e93a0183390e26485430ca2c7b5f9d33b15c74c2861cb8091/urllib3-2.4.0-py3-none-any.whl
  name: urllib3
  version: 2.4.0
  sha256: 4e16665048960a0900c702d4a66415956a584919c03361cac9f1df5c5dd7e813
  requires_dist:
  - brotli>=1.0.9 ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - h2>=4,<5 ; extra == 'h2'
  - pysocks>=1.5.6,!=1.5.7,<2.0 ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/43/99/3ec6335ded5b88c2f7ed25c56ffd952546f7ed007ffb1e1539dc3b57015a/userpath-1.9.2-py3-none-any.whl
  name: userpath
  version: 1.9.2
  sha256: 2cbf01a23d655a1ff8fc166dfb78da1b641d1ceabf0fe5f970767d380b14e89d
  requires_dist:
  - click
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/eb/fd/21a82b78173be1a2ea20f4f55154e7252bd80d21ed60b9bbbc0e2047b8d0/uv-0.6.14-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: uv
  version: 0.6.14
  sha256: 36aaeb00a70a10f748e16c7a1fc410862e2ba905806e7e9dfbc3e64596309404
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/03/2f/73ea7b15dc226f120dbc89f3f4bb40b6a5a26ac969898255f22123a94d1a/ViennaRNA-2.7.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: viennarna
  version: 2.7.0
  sha256: fd76b0e5dc671781041524f5e1c08a5be4c6d7aec21ebe2704eee06c0adb05c1
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/ef/25/b254e7c48f851be16e69d3ed8f7b67922353a870490533b1512b85e06b7c/ViennaRNA-2.7.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: viennarna
  version: 2.7.0
  sha256: fe170cde40b44d325d5ae2c48d3c1c9eaa8848bf4da8733c284afe3a2b7b09e9
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/4c/ed/3cfeb48175f0671ec430ede81f628f9fb2b1084c9064ca67ebe8c0ed6a05/virtualenv-20.30.0-py3-none-any.whl
  name: virtualenv
  version: 20.30.0
  sha256: e34302959180fca3af42d1800df014b35019490b119eba981af27f2fa486e5d6
  requires_dist:
  - distlib>=0.3.7,<1
  - filelock>=3.12.2,<4
  - importlib-metadata>=6.6 ; python_full_version < '3.8'
  - platformdirs>=3.9.1,<5
  - furo>=2023.7.26 ; extra == 'docs'
  - proselint>=0.13 ; extra == 'docs'
  - sphinx>=7.1.2,!=7.3 ; extra == 'docs'
  - sphinx-argparse>=0.4 ; extra == 'docs'
  - sphinxcontrib-towncrier>=0.2.1a0 ; extra == 'docs'
  - towncrier>=23.6 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'test'
  - coverage-enable-subprocess>=1 ; extra == 'test'
  - coverage>=7.2.7 ; extra == 'test'
  - flaky>=3.7 ; extra == 'test'
  - packaging>=23.1 ; extra == 'test'
  - pytest-env>=0.8.2 ; extra == 'test'
  - pytest-freezer>=0.4.8 ; (python_full_version >= '3.13' and platform_python_implementation == 'CPython' and sys_platform == 'win32' and extra == 'test') or (platform_python_implementation == 'GraalVM' and extra == 'test') or (platform_python_implementation == 'PyPy' and extra == 'test')
  - pytest-mock>=3.11.1 ; extra == 'test'
  - pytest-randomly>=3.12 ; extra == 'test'
  - pytest-timeout>=2.1 ; extra == 'test'
  - pytest>=7.4 ; extra == 'test'
  - setuptools>=68 ; extra == 'test'
  - time-machine>=2.10 ; platform_python_implementation == 'CPython' and extra == 'test'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
  sha256: 1b34021e815ff89a4d902d879c3bd2040bc1bd6169b32e9427497fa05c55f1ce
  md5: 75cb7132eb58d97896e173ef12ac9986
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wheel?source=hash-mapping
  size: 62931
  timestamp: 1733130309598
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  purls: []
  size: 92286
  timestamp: 1727963153079
- pypi: https://files.pythonhosted.org/packages/fc/79/edeb217c57fe1bf16d890aa91a1c2c96b28c07b46afed54a5dcf310c3f6f/zstandard-0.23.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: zstandard
  version: 0.23.0
  sha256: 72c68dda124a1a138340fb62fa21b9bf4848437d9ca60bd35db36f2d3345f373
  requires_dist:
  - cffi>=1.11 ; platform_python_implementation == 'PyPy'
  - cffi>=1.11 ; extra == 'cffi'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 567578
  timestamp: 1742433379869
