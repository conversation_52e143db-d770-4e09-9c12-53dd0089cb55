#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J rptest
#SBATCH -N 1
#SBATCH --mem=256GB
#SBATCH -t 8:00:00
#SBATCH --array=1-2
#SBATCH --output=rptest_%A_%a.out
#SBATCH --error=rptest_%A_%a.err

# Change to the rolypoly directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/rolypoly

# Define input files array - using actual test files
declare -a INPUT_FILES=(
    "test/FSEM006Q_S17_L007.anqdpht.fastq.gz"
    "test/FSEM006S_S19_L007.anqdpht.fastq.gz"
)

# Get the current input file based on array task ID
INPUT_FILE=${INPUT_FILES[$((SLURM_ARRAY_TASK_ID-1))]}

# Extract sample name for output directory
SAMPLE_NAME=$(basename ${INPUT_FILE} | sed 's/\.anqdpht\.fastq\.gz$//' | sed 's/\.fastq\.gz$//')

# Define output directory
OUTPUT_DIR="results/${SAMPLE_NAME}_rp_e2e"

# Custom database path
CUSTOM_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/resources/dna-background/mtg_template.fna"

# Log file for this job
LOG_FILE="${SAMPLE_NAME}_rolypoly_${SLURM_ARRAY_JOB_ID}_${SLURM_ARRAY_TASK_ID}.log"

echo "=== RolyPoly End-to-End Pipeline ==="
echo "Sample: ${SAMPLE_NAME}"
echo "Input: ${INPUT_FILE}"
echo "Output: ${OUTPUT_DIR}"
echo "Custom DB: ${CUSTOM_DB}"
echo "Job: ${SLURM_ARRAY_JOB_ID}_${SLURM_ARRAY_TASK_ID}"
echo "Node: $(hostname)"
echo "Date: $(date)"
echo "=================================="

# Run RolyPoly end-to-end pipeline using pixi
echo "Running RolyPoly end-to-end pipeline..."

pixi run rolypoly end2end \
    --input "${INPUT_FILE}" \
    --output-dir "${OUTPUT_DIR}" \
    --host "${CUSTOM_DB}" \
    --threads 32 \
    --memory 240g \
    --log-file "${LOG_FILE}" \
    --assembler "spades,megahit,penguin" \
    --db "neordrp" \
    --keep-tmp

# Check exit status
if [[ $? -eq 0 ]]; then
    echo "✓ Pipeline completed successfully for ${SAMPLE_NAME}"
    echo "✓ Results saved to: ${OUTPUT_DIR}"
else
    echo "✗ Pipeline failed for ${SAMPLE_NAME}"
    exit 1
fi

echo "Job completed: $(date)"
