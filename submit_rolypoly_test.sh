#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J rptest
#SBATCH -N 1
#SBATCH --mem=256GB
#SBATCH -t 8:00:00
#SBATCH --array=1-2
#SBATCH --output=logs/rptest_%A_%a.out
#SBATCH --error=logs/rptest_%A_%a.err

# Create logs directory if it doesn't exist
mkdir -p logs

# Set up environment variables
export SLURM_ARRAY_TASK_ID=${SLURM_ARRAY_TASK_ID}
export ROLYPOLY_DATA=${HOME}/rolypoly_data

# Change to the rolypoly directory
cd /clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/rolypoly

# Define input files array (adjust paths as needed)
declare -a INPUT_FILES=(
    "test/sample1_interleaved.fastq.gz"
    "test/sample2_interleaved.fastq.gz"
)

# Get the current input file based on array task ID
INPUT_FILE=${INPUT_FILES[$((SLURM_ARRAY_TASK_ID-1))]}

# Extract sample name for output directory
SAMPLE_NAME=$(basename ${INPUT_FILE} | sed 's/_interleaved\.fastq\.gz$//' | sed 's/\.fastq\.gz$//')

# Define output directory
OUTPUT_DIR="results/${SAMPLE_NAME}_rp_e2e"

# Custom database path
CUSTOM_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/resources/dna-background/mtg_template.fna"

# Log file for this job
LOG_FILE="logs/${SAMPLE_NAME}_rolypoly_${SLURM_ARRAY_JOB_ID}_${SLURM_ARRAY_TASK_ID}.log"

echo "Starting RolyPoly end-to-end pipeline for ${SAMPLE_NAME}"
echo "Input file: ${INPUT_FILE}"
echo "Output directory: ${OUTPUT_DIR}"
echo "Custom database: ${CUSTOM_DB}"
echo "Log file: ${LOG_FILE}"
echo "SLURM Job ID: ${SLURM_ARRAY_JOB_ID}_${SLURM_ARRAY_TASK_ID}"
echo "Node: $(hostname)"
echo "Date: $(date)"

# Check if input file exists
if [[ ! -f "${INPUT_FILE}" ]]; then
    echo "ERROR: Input file ${INPUT_FILE} not found!"
    exit 1
fi

# Check if custom database exists
if [[ ! -f "${CUSTOM_DB}" ]]; then
    echo "ERROR: Custom database ${CUSTOM_DB} not found!"
    exit 1
fi

# Create output directory
mkdir -p "${OUTPUT_DIR}"

# Run RolyPoly end-to-end pipeline using pixi
echo "Running RolyPoly end-to-end pipeline..."

# Method 1: Using pixi run (recommended)
pixi run rolypoly end2end \
    --input "${INPUT_FILE}" \
    --output-dir "${OUTPUT_DIR}" \
    --host "${CUSTOM_DB}" \
    --threads 32 \
    --memory 240g \
    --log-file "${LOG_FILE}" \
    --assembler "spades,megahit,penguin" \
    --db "all" \
    --keep-tmp

# Alternative Method 2: Using pixi shell (if above doesn't work)
# pixi shell --manifest-path pyproject.toml -c "
# rolypoly end2end \
#     --input '${INPUT_FILE}' \
#     --output-dir '${OUTPUT_DIR}' \
#     --host '${CUSTOM_DB}' \
#     --threads 32 \
#     --memory 240g \
#     --log-file '${LOG_FILE}' \
#     --assembler 'spades,megahit,penguin' \
#     --db 'all' \
#     --keep-tmp
# "

# Check exit status
EXIT_STATUS=$?

if [[ ${EXIT_STATUS} -eq 0 ]]; then
    echo "RolyPoly pipeline completed successfully for ${SAMPLE_NAME}"
    echo "Results saved to: ${OUTPUT_DIR}"
else
    echo "ERROR: RolyPoly pipeline failed for ${SAMPLE_NAME} with exit status ${EXIT_STATUS}"
    exit ${EXIT_STATUS}
fi

echo "Job completed at: $(date)"
