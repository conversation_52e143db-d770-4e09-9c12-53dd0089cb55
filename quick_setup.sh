#! /bin/bash

# Prints (echo) something (first arg) and also saves it to a log file (second arg) 
logit () {
   echo "$(date +"%Y-%m-%d %T") $2" | tee -a $1
}

# Default paths
CONDA_ENV_PATH="${1:-$HOME/micromamba/envs/rolypoly}"
INSTALL_PATH="${2:-$HOME/rolypoly}"
DATA_PATH="${3:-$HOME/rolypoly_data}"
LOGFILE="${4:-$HOME/RolyPoly_quick_setup.log}"
DEV_INSTALL="${5:-FALSE}"

# convert to absolute paths
CONDA_ENV_PATH=$(realpath "$CONDA_ENV_PATH")
INSTALL_PATH=$(realpath "$INSTALL_PATH")
DATA_PATH=$(realpath "$DATA_PATH")
LOGFILE=$(realpath "$LOGFILE")


# if the paths parent directories don't exist, create them
if [ ! -d "$(dirname "$CONDA_ENV_PATH")" ]; then
    logit "$LOGFILE" "Conda environment path parent directory does not exist. Creating it now    " 
    mkdir -p "$(dirname "$CONDA_ENV_PATH")"
fi

if [ ! -d "$(dirname "$INSTALL_PATH")" ]; then
    logit "$LOGFILE" "Installation path parent directory does not exist. Creating it now    "
    mkdir -p "$(dirname "$INSTALL_PATH")"
fi

if [ ! -d "$DATA_PATH" ]; then
    logit "$LOGFILE" "Data path does not exist. Creating it now    "
    mkdir -p "$DATA_PATH"
fi

# Print paths being used
logit "$LOGFILE" "Installing RolyPoly with the following paths:"
logit "$LOGFILE" "  Conda environment: $CONDA_ENV_PATH"
logit "$LOGFILE" "  Installation directory: $INSTALL_PATH"
logit "$LOGFILE" "  Data directory: $DATA_PATH"
logit "$LOGFILE" "  Logfile: $LOGFILE"

# Create directories if they don't exist
mkdir -p "$(dirname "$CONDA_ENV_PATH")"
mkdir -p "$(dirname "$INSTALL_PATH")"
mkdir -p "$DATA_PATH"

# Install mamba if needed
if ! command -v micromamba &> /dev/null && ! command -v conda &> /dev/null; then
    logit "$LOGFILE" "Neither mamba nor conda could be found. Attempting to install mamba    "
    if command -v wget &> /dev/null
    then
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            wget https://micromamba.snakepit.net/api/micromamba/linux-64/latest -O micromamba.tar.bz2
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            wget https://micromamba.snakepit.net/api/micromamba/osx-64/latest -O micromamba.tar.bz2
        elif [[ "$OSTYPE" == "msys" ]]; then
            wget https://micromamba.snakepit.net/api/micromamba/win-64/latest -O micromamba.tar.bz2
        else
            logit "$LOGFILE" "Unsupported OS: $OSTYPE"
            exit 1
        fi
        tar -xvjf micromamba.tar.bz2
        ./bin/micromamba shell init # -s bash -p ~/micromamba
        source ~/.bashrc
    else
        logit "$LOGFILE" "wget is not installed. Please install wget and try again."
        exit 1
    fi
    logit "$LOGFILE" "Mamba installed successfully."
else
    logit "$LOGFILE" "Mamba is already installed."
fi

# initialize micromamba
if command -v micromamba &> /dev/null; then
    eval "$(micromamba shell hook --shell bash)"
fi

# Get RolyPoly code
if ! command -v git &> /dev/null
then
    logit "$LOGFILE" "git could not be found. Fetching the repo from https://code.jgi.doe.gov/rolypoly/rolypoly.git"
    mkdir -p "$INSTALL_PATH"
    cd "$INSTALL_PATH" || exit
    curl -LJO https://code.jgi.doe.gov/rolypoly/rolypoly/-/archive/main/rolypoly-main.tar
    tar -xvf rolypoly-main.tar
    mv rolypoly-main/* .
    rm -rf rolypoly-main rolypoly-main.tar
else
    logit "$LOGFILE" "git is installed"
    git clone https://code.jgi.doe.gov/rolypoly/rolypoly.git "$INSTALL_PATH"
    cd "$INSTALL_PATH" || exit
fi

# Create and activate conda environment
logit "$LOGFILE" "Creating conda environment    "
micromamba create -y -p "$CONDA_ENV_PATH" -f ./src/setup/env_big.yaml   #rolypoly_recipe.yaml

# Activate environment using micromamba
micromamba activate "$CONDA_ENV_PATH"

# Install RolyPoly
logit "$LOGFILE" "Installing RolyPoly    "
if [ "$DEV_INSTALL" != "TRUE" ]; then
    logit "$LOGFILE" "Installing rolypoly-bio"
    pip install rolypoly-bio
else
    logit "$LOGFILE" "Installing RolyPoly in development mode"
    pip install -e .
fi

# Prepare external data
logit "$LOGFILE" "Preparing external data    "
export ROLYPOLY_DATA="$DATA_PATH"
rolypoly prepare-external-data --data_dir "$DATA_PATH" --log-file "$LOGFILE"

# Use micromamba for environment variable setting
if command -v micromamba &> /dev/null; then
    micromamba run -p "$CONDA_ENV_PATH" conda env config vars set ROLYPOLY_DATA="$DATA_PATH" 2>/dev/null || true
    micromamba run -p "$CONDA_ENV_PATH" conda env config vars set TAXONKIT_DB="$DATA_PATH/taxdump" 2>/dev/null || true
fi

logit "$LOGFILE" "RolyPoly installation complete!"
logit "$LOGFILE" "To start using RolyPoly:"
logit "$LOGFILE" "1. Activate the environment:"
logit "$LOGFILE" "  micromamba activate $CONDA_ENV_PATH"
logit "$LOGFILE" "2. Run RolyPoly:"
logit "$LOGFILE" "  rolypoly --help"

micromamba activate "$CONDA_ENV_PATH"
rolypoly --version >> "$LOGFILE" 2>/dev/null || logit "$LOGFILE" "Could not get rolypoly version"
rolypoly --version 2>/dev/null || echo "RolyPoly installation may need manual activation"

