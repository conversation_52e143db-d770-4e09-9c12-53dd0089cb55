{"input": "/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/rolypoly/test/FSEM006S_S19_L007.anqdpht.fastq.gz", "threads": 4, "memory": "{'bytes': '68719476736b', 'mega': '65536m', 'giga': '64g', 'tera': '0t'}", "config_file": null, "log_file": "test_local_run/rolypoly_pipeline.log", "overwrite": false, "output": "test_local_run/filtered_reads", "output_dir": "test_local_run/filtered_reads", "datadir": "/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/rolypoly/src/rolypoly/data", "keep_tmp": false, "log_level": 20, "temp_dir": "/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/rolypoly/test_local_run/filtered_reads/rolypoly_tmp_20250524_110207", "skip_existing": false, "zip_reports": false, "skip_steps": "['']", "known_dna": "/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/resources/dna-background/mtg_template.fna", "speed": 15, "override_parameters": "{}", "step_timeout": 3600, "file_name": "rp_filtered_reads", "step_params": "{'filter_known_dna': {'k': 31, 'mincovfraction': 0.7, 'hdist': 0}, 'decontaminate_rrna': {'k': 31, 'mincovfraction': 0.5, 'hdist': 0}, 'filter_identified_dna': {'k': 31, 'mincovfraction': 0.7, 'hdist': 0}, 'dedupe': {'dedupe': True, 'passes': 1, 's': 0}, 'trim_adapters': {'ktrim': 'r', 'k': 23, 'mink': 11, 'hdist': 1, 'tpe': 't', 'tbo': 't', 'minlen': 45}, 'remove_synthetic_artifacts': {'k': 31}, 'entropy_filter': {'entropy': 0.01, 'entropywindow': 30}, 'error_correct_1': {'ecco': True, 'mix': 't', 'ordered': 't'}, 'error_correct_2': {'ecc': True, 'reorder': True, 'nullifybrokenquality': True, 'passes': 1}, 'merge_reads': {'k': 93, 'extend2': 80, 'rem': True, 'mix': 'f'}, 'quality_trim_unmerged': {'qtrim': 'rl', 'trimq': 5, 'minlen': 45}}", "max_genomes": 25}