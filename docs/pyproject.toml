[project]
name = "rolypoly_bio"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "b<PERSON><PERSON>@lbl.gov" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
description = "RNA virus analysis toolkit"
readme = "README.md"
dynamic = ["version"]
license = { file = "LICENSE" }
requires-python = ">=3.9"
dependencies = [
    "bbmapy>=0.0.46,<0.0.48",
    "genomicranges>=0.6.3,<0.7",
    "iranges>=0.4.2,<0.5",
    "lightmotif>=0.9.1,<0.10",
    "mappy>=2.28,<3",
    "needletail>=0.6.3,<0.7",
    "polars>=1.26.0,<2",
    "psutil>=6.1.1,<7",
    "pyfastx>=2.2.0,<3",
    "pyhmmer>=0.11.0,<0.12",
    "pyrodigal-gv>=0.3.2,<0.4",
    "requests>=2.32.3,<3",
    "rich-click>=1.8.8,<2",
    "rich>=14.0.0,<15",
    "viennarna>=2.7.0,<3",
    "intervaltree>=3.1.0,<4"
    
]

[project.urls]
Source = "https://code.jgi.doe.gov/UNeri/rolypoly"

[project.scripts]
rolypoly = "rolypoly.rolypoly:rolypoly"

[dependency-groups]
build = ["hatch >=1.14.0,<2", "pip >=25.0.1,<26"]
format = ["ruff >=0.11.3,<0.12"]
publish = ["twine >=6.1.0,<7"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "src/rolypoly/__init__.py"

[tool.hatch.build]
packages = ["src/rolypoly"]

[tool.hatch.build.targets.wheel]
packages = ["src/rolypoly"]
package-dir = {"rolypoly_bio" = "src/rolypoly"}

[tool.pixi.workspace]
channels = ["conda-forge", "bioconda"]
platforms = ["linux-64"]

[tool.pixi.dependencies]
aragorn = ">=1.2.41,<2"
aria2 = ">=1.37.0,<2"
bowtie = ">=1.3.0,<2"
diamond = ">=2.1.10,<3"
linearfold = ">=1.0.1.dev20220829,<2"
megahit = ">=1.2.9,<2"
mmseqs2 = ">=16.747c6,<18"
ncbi-datasets-cli = ">=17.3.0,<18"
pigz = ">=2.8,<3"
plass = ">=5.cf8933,<6"
seqkit = ">=2.10.0,<3"
spades = ">=4.0.1,<5"
trnascan-se = ">=2.0.12,<3"
falco = ">=1.2.5,<2"

[tool.pixi.feature.py310.dependencies]
python = "~=3.10.0"

[tool.pixi.pypi-dependencies]
rolypoly_bio = { path = ".", editable = true }

[tool.pixi.environments]
default = { features = ["py310"] }
dev = { features = ["build", "format", "publish"] }

[tool.pixi.tasks]
test-import = "python -c 'import rolypoly; print(rolypoly.__version__)'"

[tool.pixi.feature.format.tasks]
format = "ruff check --select I --fix src && ruff format src"

[tool.pixi.feature.build.tasks]
build-pypi = "hatch version minor && hatch build"

[tool.pixi.feature.publish.tasks]
publish-pypi = "twine upload dist/*"




[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.9 (or later? TODO: check)
target-version = "py39"

[tool.ruff.lint]

ignore = ["E501", "E402", "E401","PLC0415"] # 2. Avoid enforcing line-length violations (`E501`) and not on top of file import violations (`E402`)

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
