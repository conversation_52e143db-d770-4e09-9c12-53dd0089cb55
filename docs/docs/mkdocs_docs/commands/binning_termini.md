# Termini Analysis

!!! warning "🚧 Not Implemented"
    This command is a placeholder for future development.

The `termini` command will analyze the edge regions of assembled contigs, possibly assisted by read mapping to potentially identify or determine if the contig appears truncated (and thus the genome is likely not complete). Subsequent commands will be able to use this information to improve genome binning (i.e. potentially by identifying other contigs with shared termini).
