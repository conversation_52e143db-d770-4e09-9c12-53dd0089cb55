# WIP WIP WIP WIP

Examples
========
# WIP WIP WIP WIP
Here are some example use cases for RolyPoly:

Basic Usage
-----------
<!-- 
To run the entire pipeline on a set of raw reads:

    rolypoly end-2-end -i /path/to/raw/reads -o /path/to/output/directory

Preparing External Data
-----------------------

Before running the pipeline, you may need to prepare external data:

    rolypoly prepare-external-data --data_dir /path/to/data/directory

Filtering Reads
---------------

To filter raw RNA-seq reads:

    rolypoly filter-reads -i /path/to/raw/reads -o /path/to/filtered/reads -D /path/to/known/dna.fasta

Assembly
--------

To perform assembly on filtered reads:

    rolypoly assembly -i /path/to/filtered/reads -o /path/to/assembly/output -A "spades,megahit"

Marker Gene Search
-----------

To search for RdRp sequences in assembled contigs:

    rolypoly rvirus-search -i /path/to/contigs.fasta -o /path/to/rdrp/results --db RVMT

For more detailed examples and use cases, please refer to the full
documentation or use the --help option with each command. -->
