
# WIP WIP WIP
![RolyPoly Logo](images/rp_what_went_wrong.png)

<!-- Usage
=====

RolyPoly is a command-line tool with subcommands for different stages of
the RNA virus identification pipeline. For a detailed help (in your terminal), use:

    rolypoly help

Common Options
--------------

Many commands share common options:

-   `-t, --threads`: Number of threads to use. (int, default: 1)
-   `-M, --memory`: Memory allocation in GB. (str, default: "6gb")
-   `-o, --output` or `--output-dir`: Output file or directory location.
-   `--keep-tmp`: Should temporary files and folders be saved? (optional flag, default: False)
-   `-g, --log-file`: Path to the log file.
-   `-i, --input`: Input file or directory. (str, required)

For detailed usage of each command, use the `--help` option:

    rolypoly [COMMAND] --help

Available Commands
------------------

-   prepare-external-data
-   end-2-end
-   filter-reads
-   assembly
-   rvirus-search
-   filter-assembly
-   mask-dna

For more information on each command, see the <span
role="ref">commands</span> section. -->
