Dependencies
============
All of these should be installed by mamba, if you are curios or prefer to install them/get the binaries alternatively, see the [setup*.py scripts](/misc/)

1. [SPAdes](https://github.com/ablab/spades)
2. [seqkit](https://github.com/shenwei356/seqkit)
3. [datasets](https://www.ncbi.nlm.nih.gov/datasets/docs/v2/download-and-install/)
<!-- 4. [dataformat](https://www.ncbi.nlm.nih.gov/datasets/docs/v2/download-and-install/) -->
5. [bbmap](https://sourceforge.net/projects/bbmap/)
6. [megahit](https://github.com/voutcn/megahit)
7. [fastqc](https://www.bioinformatics.babraham.ac.uk/projects/fastqc/)
8. [mmseqs](https://github.com/soedinglab/MMseqs2)
9. [plass and penguin](https://github.com/soedinglab/plass)
10. [blast](https://blast.ncbi.nlm.nih.gov/doc/blast-help/)
11. [rush](https://github.com/shenwei356/rush)
12. [diamond](https://github.com/bbuchfink/diamond)
13. [pigz](https://github.com/madler/pigz)
14. [aria2c](https://github.com/aria2/aria2)

### Python Libraries

* [polars](https://pola.rs/)
* [numpy](https://numpy.org/)
<!-- * [multiprocessing (built-in)](https://docs.python.org/3/library/multiprocessing.html) -->
* [rich_click](https://pypi.org/project/rich-click/)
* [rich](https://github.com/Textualize/rich)
* [pyhmmer](https://github.com/althonos/pyhmmer)
* [pyrodigal-gv](https://github.com/althonos/pyrodigal-gv)
* [multiprocess](https://github.com/uqfoundation/multiprocess)
* [requests](https://requests.readthedocs.io)
<!-- * [rarfile](https://github.com/markokr/rarfile) -->
<!-- * [py7zr](https://pypi.org/project/py7zr/) -->
* [pgzip](https://github.com/pgzip/pgzip)
