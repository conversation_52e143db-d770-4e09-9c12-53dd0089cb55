site_name: RolyPoly Documentation (MkDocs)
site_url: "https://pages.jgi.doe.gov/rolypoly-uneri-mkdocs/"
repo_url: https://code.jgi.doe.gov/UNeri/rolypoly
docs_dir: docs/mkdocs_docs
theme:
  name: material
  logo: images/rp_pixel.png
  favicon: images/rp_pixel.png
  icon: images/rp_pixel.png
  palette:
    - scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.instant
    - navigation.tracking
    - navigation.sections
    - navigation.expand
    - toc.follow
    - search.suggest
    - search.highlight

plugins:
    - search
    - mkdocstrings:
        handlers:
          python:
            paths: [src]
            options:
              show_source: false
              show_root_heading: true
              show_object_full_path: false
              show_category_heading: true
              show_if_no_docstring: true
              filters: ["!^_"]
              docstring_style: google
              docstring_section_style: table
              show_signature: true
              show_signature_annotations: true
              separate_signature: true
              line_length: 80
              merge_init_into_class: true
              show_bases: true
              show_submodules: true
              group_by_category: true
              heading_level: 2
              members_order: source
              extra:
                setup_commands:
                  - import sys
                  - from pathlib import Path
                  - sys.path.append(str(Path("src").resolve()))
    - mermaid2:
        version: 10.4.0
        arguments:
          securityLevel: 'loose'
          startOnLoad: true
          theme: |
            ^(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) ? 'dark' : 'default'

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:mermaid2.fence_mermaid_custom
        - name: python
          class: python
          format: !!python/name:pymdownx.superfences.fence_code_format
  - admonition
  - pymdownx.details
  - pymdownx.tabbed:
      alternate_style: true
  - tables
  - attr_list
  - md_in_html
  - toc:
      permalink: true
      toc_depth: 3

nav:
  - Home: index.md
  - About: about.md
  - Installation: installation.md
  # - Dependencies: dependencies.md
  - Configuration: configuration.md
  - Commands:
    - Overview: commands/index.md
    - External Data: commands/prepare_external_data.md
    - End to End: commands/end_to_end.md
    - Read Processing: commands/read_processing.md
    - Assembly: commands/assembly.md
    - Marker Gene Search: commands/marker_search.md
    - Assembly Filtering: commands/filter_assembly.md
    - Virus Search: commands/search_viruses.md
    - RNA Annotation: commands/annotate_rna.md
    - Protein Annotation: commands/annotate_prot.md
    - Host Classification: commands/host_classify.md
    - Binning:
      - Termini Analysis: commands/binning_termini.md
      - Correlation Analysis: commands/binning_correlate.md
  - Workflow: workflow.md
  # - Examples: examples.md
  - Resource Usage: resource_usage.md
  - FAQ: FAQ.md
  - Tips and Tricks: tips_and_tricks.md
  - Contributing: contribute.md
  - Citation: citation.md