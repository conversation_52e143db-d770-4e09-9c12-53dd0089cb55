# Changelog

## 16.04.2025 [Unreleased]

### Added
- Added a rust module for miscellaneous utilities (under development - sequence translation, deduplication)

### Fixed
- marker-search: corrected the args and variable names (less abbreviations).

## 14.04.2025 [Unreleased]

### Changed
- Improved path handling in read filtering pipeline:
  - All temporary files now use absolute paths.
  - Removed dependency on change_directory (dropped)
- Added zip_reports option to compress run_info directory after processing
- Dropped fastqc and multiqc - moved to falco.

### Fixed
- filter-reads cleanup phase


##  07.02.2025 [Unreleased]

### Added
- Added placeholders documentation (MkDocs) for the under development features:
  - Binning (termini, correlation)
  - Co-assembly
  - Annotation
  - Host classification
- Added workflow charts to the docs (mermaid)

### Changed
- Dependency management moved to an env_file instead of pyproject.toml
- Recommended installation now curls the quick_setup.sh script and uses micromamba. 
- Restructured project layout (src/, docs/, misc/)
- Added a logging level flag to the command line interface / internals for more/less verbose output (i.e. for debugging)
- Configuration handling: 
- ROLOPOLY_DATA is now set on installation. The citations json is now bundled with the data.

### Fixed
- Assembly pipeline error handling.
- Assembly pipeline uses seqkit rmdup instead of mmseqs linclust to remove duplicates, as it appears that chimeras from one assmebler tend to be longer than the non-chimeric contigs outputted by another assmebler, thus in linclust they are "absorbed" by the chimeric contigs.

### Infrastructure
- GitLab CI/CD for documentation
- Development environment setup scripts
- Benchmarking utilities

### Dependencies
  - BBMap, FastQC, MultiQC
  - MMseqs2, BLAST, Prodigal
  - Minimap2, Bowtie

### Notes
- Project under active development
- Some features experimental/incomplete