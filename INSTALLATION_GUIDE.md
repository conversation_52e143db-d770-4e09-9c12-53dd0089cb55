# RolyPoly Installation Guide

This guide explains how to properly install RolyPoly and download the required databases.

## 🚀 NEW: One-Command Setup with Pixi Tasks

The easiest way to set up RolyPoly is now with the enhanced Pixi tasks:

```bash
# Clone and setup in one go
git clone https://code.jgi.doe.gov/rolypoly/rolypoly.git
cd rolypoly

# Complete setup with default database location
pixi run setup-complete

# OR with custom database location
ROLYPOLY_DATA=/path/to/your/data pixi run setup-complete
```

**Available Pixi Tasks** (run `pixi task list` to see all):
- `setup-complete` - Complete setup: install + databases
- `setup-databases` - Download databases only  
- `setup-quick` - Quick setup (2 threads)
- `info` - Show status and database info
- `verify` - Verify installation
- `db-info` - Show database sizes
- `db-clean` - Remove databases

See [PIXI_USAGE_GUIDE.md](PIXI_USAGE_GUIDE.md) for detailed task documentation.

## Issues with the Original `quick_setup.sh` Script

The original `quick_setup.sh` script had several problems:

1. **Parameter logic error**: The script used `$(command -v micromamba &> /dev/null)` which always returns empty string, causing logic errors
2. **Mixed conda/micromamba usage**: The script tried to use conda commands with micromamba environments
3. **Incorrect conda.sh path**: Tried to source conda scripts that don't exist for micromamba
4. **Missing error handling**: Commands failed silently without proper error reporting

## ✅ SOLUTION 1: Use Pixi (Recommended)

The modern and recommended way to install RolyPoly is using **Pixi**, which is the package manager this project is configured for.

### Installation Steps:

1. **Clone the repository** (if not already done):
   ```bash
   git clone https://code.jgi.doe.gov/rolypoly/rolypoly.git
   cd rolypoly
   ```

2. **Install using the new Pixi setup script**:
   ```bash
   bash pixi_setup.sh [DATA_DIRECTORY] [LOG_FILE]
   ```
   
   Or with custom paths:
   ```bash
   bash pixi_setup.sh /path/to/your/data/directory /path/to/logfile.log
   ```

3. **OR use the new Pixi tasks (even easier)**:
   ```bash
   # Complete setup
   pixi run setup-complete
   
   # With custom database location
   ROLYPOLY_DATA=/path/to/data pixi run setup-complete
   ```

4. **Use RolyPoly**:
   ```bash
   pixi run rolypoly --help
   pixi run rolypoly marker-search --help
   ```

### Manual Pixi Installation (if needed):

If Pixi is not installed on your system:
```bash
curl -fsSL https://pixi.sh/install.sh | bash
export PATH="$HOME/.pixi/bin:$PATH"
```

## ✅ SOLUTION 2: Fixed `quick_setup.sh` Script

I've fixed the original `quick_setup.sh` script to resolve the issues mentioned above. The corrected version:

1. ✅ Fixed the conda/micromamba detection logic
2. ✅ Uses micromamba consistently throughout
3. ✅ Fixes the conda.sh path issue
4. ✅ Added proper error handling

You can now use the fixed script:
```bash
bash quick_setup.sh [CONDA_ENV_PATH] [INSTALL_PATH] [DATA_PATH] [LOGFILE] [DEV_INSTALL]
```

## Database Information

### Download URL
The databases are pre-built and hosted at:
```
https://portal.nersc.gov/dna/microbial/prokpubs/rolypoly/data/data.tar.gz
```

### Database Contents
The downloaded databases include (~7.1 GB total):

- **HMM Profile Databases (3.7 GB)**:
  - RdRp-scan profiles (RNA-dependent RNA polymerase markers)
  - RVMT (RNA Virus MetaTranscriptomes) profiles
  - Pfam-A v37 RdRp and reverse transcriptase profiles
  - GeNomad RNA viral markers
  - NeoRdRp v2.1 profiles
  - TSA 2018 viral profiles

- **Reference Sequence Databases**:
  - rRNA databases (1.3 GB) - SILVA 138 ribosomal RNA sequences
  - RVMT contigs (750 MB) - RNA virus metatranscriptomes
  - Rfam (645 MB) - RNA family covariance models
  - Masking sequences (503 MB) - For contamination detection
  - NCBI Ribovirus (62 MB) - Reference viral sequences

- **Taxonomy Data (451 MB)**:
  - NCBI taxonomy dump for taxonomic classification

### Database Download Options

1. **Standard download** (default, recommended):
   ```bash
   # Using Pixi tasks
   pixi run setup-databases
   
   # Or direct command
   pixi run rolypoly prepare-data --ROLYPOLY_DATA /path/to/data
   ```

2. **Build from scratch** (--try-hard mode, takes much longer but more up-to-date):
   ```bash
   # Using Pixi tasks
   pixi run setup-databases-fresh
   
   # Or direct command
   pixi run rolypoly prepare-data --try-hard --threads 8 --ROLYPOLY_DATA /path/to/data
   ```

## Environment Variables

### Default Configuration
Database location is automatically set via `pyproject.toml`:
```toml
[tool.pixi.activation]
env = { ROLYPOLY_DATA = "$HOME/rolypoly_data" }
```

### Custom Configuration
Set the data directory for RolyPoly:
```bash
# Temporary
export ROLYPOLY_DATA=/path/to/your/data/directory

# Permanent (edit pyproject.toml)
[tool.pixi.activation]
env = { ROLYPOLY_DATA = "/your/custom/path" }
```

## Verification

Test your installation:
```bash
# Using Pixi tasks
pixi run verify
pixi run info

# Or direct commands
pixi run rolypoly --version
pixi run rolypoly --help
```

## Usage Examples

```bash
# Set data directory (if not using default)
export ROLYPOLY_DATA=/path/to/your/data

# Filter reads
pixi run rolypoly filter-reads --input reads.fastq.gz --output filtered/

# Search for viral markers
pixi run rolypoly marker-search --input contigs.fasta --output results/

# Run end-to-end pipeline
pixi run rolypoly end2end --reads reads.fastq.gz --output results/
```

## Troubleshooting

### Common Issues:

1. **"rolypoly command not found"**: Make sure you're using `pixi run rolypoly` or have activated the environment properly

2. **Database download fails**: Check internet connectivity and disk space (need ~7.1 GB free space)

3. **Permission errors**: Make sure you have write permissions to the data directory

4. **Out of memory**: Some operations require significant RAM; reduce `--threads` if needed

### Getting Help:

- Check the Pixi tasks: `pixi task list`
- Check status: `pixi run info`
- Check the documentation: https://pages.jgi.doe.gov/rolypoly/docs/
- Open an issue: https://code.jgi.doe.gov/rolypoly/rolypoly/-/issues
- Use the built-in help: `pixi run rolypoly COMMAND --help`

## Summary

✅ **NEW: One-command setup** with Pixi tasks  
✅ **Configurable database location** via environment variables or config  
✅ **Use Pixi** (modern, recommended approach)  
✅ **Fixed quick_setup.sh** (corrected legacy approach)  
✅ **Automatic database download** via `prepare-data` command  
✅ **~7.1 GB of curated databases** for RNA virus analysis  

The databases are automatically downloaded from NERSC and include all the necessary HMM profiles, reference sequences, and taxonomy data needed for comprehensive RNA virus analysis. 