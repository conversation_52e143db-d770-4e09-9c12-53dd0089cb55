[project]
name = "rolypoly_bio"
dynamic = ["version"]
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "b<PERSON><PERSON>@lbl.gov" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
description = "RNA virus analysis toolkit"
readme = "README.md"
requires-python = ">=3.9"
license = { file = "LICENSE" }
dependencies = [
    "bbmapy>=0.0.46,<0.0.48",
    "dna_features_viewer>=3.1.4,<4",
    "genomicranges>=0.6.3,<0.7",
    "intervaltree>=3.1.0,<4",
    "iranges>=0.4.2,<0.5",
    "lightmotif>=0.9.1,<0.10",
    "mappy>=2.28,<3",
    "matplotlib>=3.10.1,<4",
    "multiprocess>=0.70.0,<1",
    "needletail>=0.6.3,<0.7",
    "numpy>=1.24.0,<2",
    "pgzip>=0.3.0,<1",
    "polars>=1.26.0,<2",
    "psutil>=6.1.1,<7",
    "pyfastx>=2.2.0,<3",
    "pyhmmer>=0.11.0,<0.12",
    "pymsaviz>=0.5.0,<0.6",
    "pyranges>=0.1.4,<0.2",
    "pyrodigal-gv>=0.3.2,<0.4",
    "requests>=2.32.3,<3",
    "rich-click>=1.8.8,<2",
    "rich>=14.0.0,<15",
    "viennarna>=2.7.0,<3",
]

[project.urls]
Source = "https://code.jgi.doe.gov/rolypoly/rolypoly"
Documentation = "https://pages.jgi.doe.gov/rolypoly/docs/"
pypi = "https://pypi.org/project/rolypoly-bio/"

[project.scripts]
rolypoly = "rolypoly.rolypoly:rolypoly"

[dependency-groups]
build = ["hatch >=1.14.0,<2", "pip >=25.0.1,<26"]
format = ["ruff >=0.11.3,<0.12"]
publish = ["twine >=6.1.0,<7"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.version]
path = "src/rolypoly/__init__.py"

[tool.hatch.build]
packages = ["src/rolypoly"]

[tool.hatch.build.targets.wheel]
packages = ["src/rolypoly"]
package-dir = {"rolypoly_bio" = "src/rolypoly"}

[tool.pixi.workspace]
channels = ["conda-forge", "bioconda"]
platforms = ["linux-64"]

[tool.pixi.pypi-dependencies]
rolypoly_bio = { path = ".", editable = true }

[tool.pixi.dependencies]
aragorn = ">=1.2.41,<2"
aria2 = ">=1.37.0,<2"
bowtie = ">=1.3.0,<2"
diamond = ">=2.1.10,<3"
falco = ">=1.2.5,<2"
infernal = ">=1.1.5,<2"
linearfold = ">=1.0.1.dev20220829,<2"
megahit = ">=1.2.9,<2"
mmseqs2 = ">=16.747c6,<18"
ncbi-datasets-cli = ">=17.3.0,<18"
pigz = ">=2.8,<3"
pip = ">=24.0"
plass = ">=5.cf8933,<6"
seqkit = ">=2.10.0,<3"
spades = ">=4.0.1,<5"
trnascan-se = ">=2.0.12,<3"

[tool.pixi.feature.py39.dependencies]
python = "~=3.9.0"

[tool.pixi.environments]
default = { features = ["py39"] }
dev = { features = ["build", "format", "publish","test"] }

# Environment variables for database configuration
[tool.pixi.activation]
env = { ROLYPOLY_DATA = "$HOME/rolypoly_data" }

[tool.pixi.feature.format.tasks]
format = "ruff check --select I --fix src && ruff format src"

[tool.pixi.feature.publish.tasks]
publish-pypi = "twine upload dist/* --verbose"

[tool.pixi.feature.build.tasks]
build-pypi = "hatch version micro && hatch build --clean"

[tool.pixi.feature.test.tasks]
test-import = "python -c 'import rolypoly; print(rolypoly.__version__)'"

# RolyPoly Setup and Database Management Tasks
[tool.pixi.tasks]

# Complete setup - installs dependencies and downloads databases
setup-complete = { cmd = "pixi run setup-install && pixi run setup-databases", description = "Complete RolyPoly setup: install dependencies and download databases" }

# Install RolyPoly without databases
setup-install = { cmd = "echo 'RolyPoly dependencies already installed via pixi install'", description = "Install RolyPoly dependencies (automatically done by pixi)" }

# Setup databases with default location
setup-databases = { cmd = "rolypoly prepare-data --threads 4", description = "Download RolyPoly databases to default location ($ROLYPOLY_DATA)" }

# Setup databases with custom location
setup-databases-custom = { cmd = "rolypoly prepare-data --ROLYPOLY_DATA", description = "Download databases to custom location (usage: pixi run setup-databases-custom /path/to/data)" }

# Setup databases from scratch (slower but more up-to-date)
setup-databases-fresh = { cmd = "rolypoly prepare-data --try-hard --threads 8", description = "Build databases from scratch (slower but more up-to-date)" }

# Quick setup for development/testing with smaller thread count
setup-quick = { cmd = "rolypoly prepare-data --threads 2", description = "Quick database setup with 2 threads" }

# Verify installation
verify = { cmd = "rolypoly --version && rolypoly --help", description = "Verify RolyPoly installation" }

# Show database info
db-info = { cmd = "du -sh $ROLYPOLY_DATA/* 2>/dev/null || echo 'Database directory not found or empty'", description = "Show database sizes and contents" }

# Update databases
db-update = { cmd = "rolypoly update data", description = "Update RolyPoly databases" }

# Clean databases (removes all downloaded data)
db-clean = { cmd = "rm -rf $ROLYPOLY_DATA && echo 'Database directory cleaned'", description = "Remove all downloaded databases" }

# Set custom database location (usage: pixi run db-set-location /path/to/data)
db-set-location = { cmd = "export ROLYPOLY_DATA", description = "Set custom database location for current session" }

# Example analysis tasks
example-help = { cmd = "rolypoly --help", description = "Show RolyPoly help" }
example-marker-search = { cmd = "rolypoly marker-search --help", description = "Show marker search help" }
example-filter-reads = { cmd = "rolypoly filter-reads --help", description = "Show read filtering help" }
example-end2end = { cmd = "rolypoly end2end --help", description = "Show end-to-end pipeline help" }

# Development and testing tasks
dev-test = { cmd = "python -m pytest tests/ -v", description = "Run tests (if available)" }
dev-format = { cmd = "ruff check --select I --fix src && ruff format src", description = "Format code with ruff" }
dev-check = { cmd = "ruff check src", description = "Check code style with ruff" }

# Maintenance tasks
clean-logs = { cmd = "rm -f *.log *_logfile.txt prepare_external_data_logfile.txt", description = "Clean up log files" }
clean-temp = { cmd = "rm -rf tmp/ temp/ __pycache__/ .pytest_cache/", description = "Clean temporary files" }
clean-all = { cmd = "pixi run clean-logs && pixi run clean-temp", description = "Clean logs and temporary files" }

# Info and status tasks
info = { cmd = "echo 'RolyPoly Information:' && rolypoly --version && echo 'Database location: $ROLYPOLY_DATA' && echo 'Database status:' && pixi run db-info", description = "Show RolyPoly information and status" }
status = { cmd = "pixi run info", description = "Alias for info command" }

# Environment management
env-show = { cmd = "env | grep ROLYPOLY", description = "Show RolyPoly environment variables" }
env-activate = { cmd = "echo 'export ROLYPOLY_DATA=$ROLYPOLY_DATA' && echo 'RolyPoly environment ready. Use: rolypoly --help'", description = "Show environment activation commands" }


[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

line-length = 88
indent-width = 4
target-version = "py39"

[tool.ruff.lint]
ignore = ["E501", "E402", "E401","PLC0415"]
fixable = ["ALL"]
unfixable = []
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
line-ending = "auto"
