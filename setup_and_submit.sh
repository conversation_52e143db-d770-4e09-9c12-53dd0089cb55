#!/bin/bash

# Setup and submission helper script for RolyPoly SLURM job
# This script helps prepare the environment and submit the SLURM job

set -e  # Exit on any error

echo "=== RolyPoly SLURM Job Setup and Submission ==="
echo "Date: $(date)"
echo "Working directory: $(pwd)"

# Check if we're in the right directory
if [[ ! -f "pyproject.toml" ]]; then
    echo "ERROR: pyproject.toml not found. Please run this script from the rolypoly root directory."
    exit 1
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p logs
mkdir -p results
mkdir -p test  # Create test directory if it doesn't exist

# Check if pixi is available
if ! command -v pixi &> /dev/null; then
    echo "ERROR: pixi command not found. Please install pixi first."
    echo "Visit: https://pixi.sh/latest/"
    exit 1
fi

# Check if the custom database exists
CUSTOM_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/ww/oakland/opdetect_combined/resources/dna-background/mtg_template.fna"
if [[ ! -f "${CUSTOM_DB}" ]]; then
    echo "ERROR: Custom database not found at: ${CUSTOM_DB}"
    exit 1
else
    echo "✓ Custom database found: ${CUSTOM_DB}"
fi

# Check for test files
echo "Checking for test files..."
TEST_FILES=(
    "test/sample1_interleaved.fastq.gz"
    "test/sample2_interleaved.fastq.gz"
)

MISSING_FILES=()
for file in "${TEST_FILES[@]}"; do
    if [[ ! -f "${file}" ]]; then
        MISSING_FILES+=("${file}")
    else
        echo "✓ Found: ${file}"
    fi
done

if [[ ${#MISSING_FILES[@]} -gt 0 ]]; then
    echo "WARNING: Missing test files:"
    for file in "${MISSING_FILES[@]}"; do
        echo "  - ${file}"
    done
    echo ""
    echo "Please create or symlink your test files to these locations."
    echo "Example commands:"
    echo "  ln -s /path/to/your/actual/sample1.fastq.gz test/sample1_interleaved.fastq.gz"
    echo "  ln -s /path/to/your/actual/sample2.fastq.gz test/sample2_interleaved.fastq.gz"
    echo ""
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Exiting. Please set up test files first."
        exit 1
    fi
fi

# Verify pixi environment
echo "Verifying pixi environment..."
if ! pixi run rolypoly --version &> /dev/null; then
    echo "ERROR: RolyPoly not accessible via pixi. Please run 'pixi install' first."
    exit 1
else
    echo "✓ RolyPoly accessible via pixi"
    ROLYPOLY_VERSION=$(pixi run rolypoly --version 2>/dev/null || echo "unknown")
    echo "  Version: ${ROLYPOLY_VERSION}"
fi

# Check SLURM availability
if ! command -v sbatch &> /dev/null; then
    echo "ERROR: sbatch command not found. Are you on a SLURM cluster?"
    exit 1
else
    echo "✓ SLURM available"
fi

# Show job configuration
echo ""
echo "=== Job Configuration ==="
echo "Account: grp-org-sc-mgs"
echo "Queue: jgi_normal"
echo "Job name: rptest"
echo "Memory: 256GB"
echo "Time limit: 8:00:00"
echo "Array tasks: 1-2"
echo "Threads per task: 32"
echo "Custom database: ${CUSTOM_DB}"
echo "Include rRNA step: YES (default behavior)"
echo ""

# Final confirmation
read -p "Submit the SLURM job now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Submitting SLURM job..."
    
    # Make the submission script executable
    chmod +x submit_rolypoly_test.sh
    
    # Submit the job
    JOB_ID=$(sbatch submit_rolypoly_test.sh | awk '{print $4}')
    
    if [[ $? -eq 0 ]]; then
        echo "✓ Job submitted successfully!"
        echo "Job ID: ${JOB_ID}"
        echo ""
        echo "Monitor your job with:"
        echo "  squeue -j ${JOB_ID}"
        echo "  squeue -u $(whoami)"
        echo ""
        echo "Check logs in:"
        echo "  logs/rptest_${JOB_ID}_*.out"
        echo "  logs/rptest_${JOB_ID}_*.err"
        echo ""
        echo "Results will be saved to:"
        echo "  results/sample1_rp_e2e/"
        echo "  results/sample2_rp_e2e/"
    else
        echo "ERROR: Job submission failed!"
        exit 1
    fi
else
    echo "Job submission cancelled."
    echo ""
    echo "To submit manually later, run:"
    echo "  sbatch submit_rolypoly_test.sh"
fi

echo ""
echo "Setup complete!"
