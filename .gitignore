rolypoly/pyutils/__pycache__
rolypoly/pyutils/__pycache__*
pyutils/__pycache__/mask_dna.cpython-310.pyc
pyutils/__pycache__/
pyutils/__pycache__/*
*.pyc
rolypoly.egg-info/
external_dependencies/
external_dependencies/*
data
data/*

obsolete/
obsolete/*
misc/bash_scripts/*
__pycache__/*
*.pyc
__pycache__/
**/__pycache__/**
misc/exprimental/litrature_mining
misc//obsolete
build
rolypoly/build/*
*build/*
*.egg-info


.ruff_cache

dist
*.egg-info
tests
.pytest_cache
*logfile.log

# results
# results/
TODO.tsv
#smulated inputs
# simulated_inputs/
#
#logs
logs/

# continue
.continueignore
.continuerules

# outputs
**/tmp**
**log.txt

# Pixi environments
.pixi
*.egg-info

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
__pycache__/

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
tests/
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# UV
uv.lock

# poetry
poetry.lock

#conda

# pdm
pdm.lock
.pdm.toml
.pdm-python
.pdm-build/

__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# PyPI configuration file
.pypirc

# vscode
.vscode/
# quarto
.quarto/
public/
/.quarto/

# llms
.continueignore
.continuerules
.cursorignore
.cursorrules
.cursor
