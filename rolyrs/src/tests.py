from rolyrs import (
    translate_six_frame,
    reverse_complement,
    filter_by_header,
)

def test_reverse_complement():
    """Test reverse complement function"""
    assert reverse_complement("ATGC") == "GCAT"
    assert reverse_complement("NNNN") == "NNNN"
    print("✓ reverse_complement test passed")

def test_translate():
    """Test translation function"""
    # Test standard genetic code
    result = translate_six_frame("ATGAAATAG", genetic_code_id=1)
    assert result["frame_1"] == "MK*"  # ATG AAA TAG
    assert "frame_-1" in result  # Verify reverse frames exist
    print("✓ translate test passed")

def test_filter():
    """Test sequence filtering"""
    # Create a test FASTA file
    with open("test.fasta", "w") as f:
        f.write(">seq1 test\nATGC\n>seq2 keep\nGCAT\n>seq3 test\nTGCA\n")
    
    # Filter sequences containing "keep"
    count = filter_by_header("test.fasta", ["keep"], "filtered.fasta", None)
    assert count == 1  # Should find one sequence
    
    # Verify the output
    with open("filtered.fasta") as f:
        content = f.read()
        assert ">seq2 keep" in content
        assert ">seq1 test" not in content
    print("✓ filter test passed")

if __name__ == "__main__":
    print("Running tests...")
    test_reverse_complement()
    test_translate()
    test_filter()
    print("\nAll tests passed! 🎉") 