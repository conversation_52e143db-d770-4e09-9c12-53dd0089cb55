version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backports-1.0-pyhd8ed1ab_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backports.tarfile-1.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.43-h4bf12b8_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.1.31-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cmarkgfm-2024.11.20-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.10-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cryptography-44.0.2-py312hda17c39_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/docutils-0.21.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-14.2.0-hdb7739f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/id-1.5.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.6.1-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.5.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.classes-3.4.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.context-6.0.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.functools-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jeepney-0.9.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-3.10.0-he073ed8_18.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/keyring-25.6.0-pyha804496_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-14.2.0-h9c4974d_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.1-h2ff4ddf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-14.2.0-hed042b8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.1-hee588c1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-h8f9b012_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/maturin-1.8.3-py312h6ab59e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/more-itertools-10.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nh3-0.2.21-py39h77e2912_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-24.2-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.44-hba22ea6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.0.1-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.10-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.10-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.12-6_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/readme_renderer-44.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-toolbelt-1.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ruff-0.11.5-py312h286b59f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rust-1.86.0-h1a8d7c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rust-std-x86_64-unknown-linux-gnu-1.86.0-h2c6d0dc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/secretstorage-3.3.3-py312h7900ff3_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-78.1.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.17-h0157908_18.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/twine-6.1.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.13.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.21.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_1.conda
      - pypi: .
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
  sha256: a3967b937b9abf0f2a99f3173fa4630293979bd1644709d89580e7c62a544661
  md5: aaa2a381ccc56eac91d63b6c1240312f
  depends:
  - cpython
  - python-gil
  license: MIT
  license_family: MIT
  purls: []
  size: 8191
  timestamp: 1744137672556
- conda: https://conda.anaconda.org/conda-forge/noarch/backports-1.0-pyhd8ed1ab_5.conda
  sha256: e1c3dc8b5aa6e12145423fed262b4754d70fec601339896b9ccf483178f690a6
  md5: 767d508c1a67e02ae8f50e44cacfadb2
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 7069
  timestamp: 1733218168786
- conda: https://conda.anaconda.org/conda-forge/noarch/backports.tarfile-1.2.0-pyhd8ed1ab_1.conda
  sha256: a0f41db6d7580cec3c850e5d1b82cb03197dd49a3179b1cee59c62cd2c761b36
  md5: df837d654933488220b454c6a3b0fad6
  depends:
  - backports
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/backports-tarfile?source=hash-mapping
  size: 32786
  timestamp: 1733325872620
- conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.43-h4bf12b8_4.conda
  sha256: 194d771be287dc973f6057c0747010ce28adf960f38d6e03ce3e828d7b74833e
  md5: ef67db625ad0d2dce398837102f875ed
  depends:
  - ld_impl_linux-64 2.43 h712a8e2_4
  - sysroot_linux-64
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 6111717
  timestamp: 1740155471052
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_2.conda
  sha256: f2a59ccd20b4816dea9a2a5cb917eb69728271dbf1aeab4e1b7e609330a50b6f
  md5: b0b867af6fc74b2a0aa206da29c0f3cf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_2
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=hash-mapping
  size: 349867
  timestamp: 1725267732089
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
  sha256: bf832198976d559ab44d6cdb315642655547e26d826e34da67cbee6624cda189
  md5: 19f3a56f68d2fd06c516076bff482c52
  license: ISC
  purls: []
  size: 158144
  timestamp: 1738298224464
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.1.31-pyhd8ed1ab_0.conda
  sha256: 42a78446da06a2568cb13e69be3355169fbd0ea424b00fc80b7d840f5baaacf3
  md5: c207fa5ac7ea99b149344385a9c0880d
  depends:
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/certifi?source=compressed-mapping
  size: 162721
  timestamp: 1739515973129
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
  sha256: cba6ea83c4b0b4f5b5dc59cb19830519b28f95d7ebef7c9c5cf1c14843621457
  md5: a861504bbea4161a9170b85d4d2be840
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=hash-mapping
  size: 294403
  timestamp: 1725560714366
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.1-pyhd8ed1ab_0.conda
  sha256: 4e0ee91b97e5de3e74567bdacea27f0139709fceca4db8adffbe24deffccb09b
  md5: e83a31202d1c0a000fce3e9cf3825875
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/charset-normalizer?source=hash-mapping
  size: 47438
  timestamp: 1735929811779
- conda: https://conda.anaconda.org/conda-forge/linux-64/cmarkgfm-2024.11.20-py312h66e93f0_0.conda
  sha256: 294643f1ad5cbaa8646f803b89cc2da2b43c41cf4d3855883662ab0bb5455d3e
  md5: bf99b4a864e31ecd9244affd27f3ceb6
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.0.0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cmarkgfm?source=hash-mapping
  size: 139452
  timestamp: 1732193337513
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.10-py312hd8ed1ab_0.conda
  noarch: generic
  sha256: acb47715abf1cd8177a5c20f42a34555b5d9cebb68ff39a58706e84effe218e2
  md5: 7584a4b1e802afa25c89c0dcc72d0826
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi * *_cp312
  license: Python-2.0
  purls: []
  size: 45861
  timestamp: 1744323195619
- conda: https://conda.anaconda.org/conda-forge/linux-64/cryptography-44.0.2-py312hda17c39_0.conda
  sha256: df45e7c376d2dbdac6cedace080164b33a15b2f2f3ef8920e0ea934f9d87fd7b
  md5: 9b4ab17c7654fe98ef6cd9a0021cd7bb
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.12
  - libgcc >=13
  - openssl >=3.4.1,<4.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: Apache-2.0 AND BSD-3-Clause AND PSF-2.0 AND MIT
  license_family: BSD
  purls:
  - pkg:pypi/cryptography?source=hash-mapping
  size: 1590060
  timestamp: 1740893871300
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
  sha256: 8f5f995699a2d9dbdd62c61385bfeeb57c82a681a7c8c5313c395aa0ccab68a5
  md5: ecfff944ba3960ecb334b9a2663d708d
  depends:
  - expat >=2.4.2,<3.0a0
  - libgcc-ng >=9.4.0
  - libglib >=2.70.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 618596
  timestamp: 1640112124844
- conda: https://conda.anaconda.org/conda-forge/noarch/docutils-0.21.2-pyhd8ed1ab_1.conda
  sha256: fa5966bb1718bbf6967a85075e30e4547901410cc7cb7b16daf68942e9a94823
  md5: 24c1ca34138ee57de72a943237cde4cc
  depends:
  - python >=3.9
  license: CC-PDDC AND BSD-3-Clause AND BSD-2-Clause AND ZPL-2.1
  purls:
  - pkg:pypi/docutils?source=hash-mapping
  size: 402700
  timestamp: 1733217860944
- conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.0-h5888daf_0.conda
  sha256: dd5530ddddca93b17318838b97a2c9d7694fa4d57fc676cf0d06da649085e57a
  md5: d6845ae4dea52a2f90178bf1829a21f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat 2.7.0 h5888daf_0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 140050
  timestamp: 1743431809745
- conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-14.2.0-hdb7739f_2.conda
  sha256: bbbc4baa66558f4d1805ff7f81050bfe798f2f0ca24f6b509c5c5d152f72bfbe
  md5: 2d9b7363abe1f9aaf1fe129b215371e3
  depends:
  - binutils_impl_linux-64 >=2.40
  - libgcc >=14.2.0
  - libgcc-devel_linux-64 14.2.0 h9c4974d_102
  - libgomp >=14.2.0
  - libsanitizer 14.2.0 hed042b8_2
  - libstdcxx >=14.2.0
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 73545585
  timestamp: 1740240767348
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/h2?source=hash-mapping
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hpack?source=hash-mapping
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hyperframe?source=hash-mapping
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/noarch/id-1.5.0-pyh29332c3_0.conda
  sha256: 161e3eb5aba887d0329bb4099f72cb92eed9072cf63f551d08540480116e69a2
  md5: d37314c8f553e3b4b44d113a0ee10196
  depends:
  - python >=3.9
  - requests
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/id?source=hash-mapping
  size: 24444
  timestamp: 1737528654512
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/idna?source=hash-mapping
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.6.1-pyha770c72_0.conda
  sha256: 598951ebdb23e25e4cec4bbff0ae369cec65ead80b50bc08b441d8e54de5cf03
  md5: f4b39bf00c69f56ac01e020ebfac066c
  depends:
  - python >=3.9
  - zipp >=0.5
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/importlib-metadata?source=hash-mapping
  size: 29141
  timestamp: 1737420302391
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.5.2-pyhd8ed1ab_0.conda
  sha256: acc1d991837c0afb67c75b77fdc72b4bf022aac71fedd8b9ea45918ac9b08a80
  md5: c85c76dc67d75619a92f51dfbce06992
  depends:
  - python >=3.9
  - zipp >=3.1.0
  constrains:
  - importlib-resources >=6.5.2,<6.5.3.0a0
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/importlib-resources?source=hash-mapping
  size: 33781
  timestamp: 1736252433366
- conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.classes-3.4.0-pyhd8ed1ab_2.conda
  sha256: 3d16a0fa55a29fe723c918a979b2ee927eb0bf9616381cdfd26fa9ea2b649546
  md5: ade6b25a6136661dadd1a43e4350b10b
  depends:
  - more-itertools
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jaraco-classes?source=hash-mapping
  size: 12109
  timestamp: 1733326001034
- conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.context-6.0.1-pyhd8ed1ab_0.conda
  sha256: bfaba92cd33a0ae2488ab64a1d4e062bcf52b26a71f88292c62386ccac4789d7
  md5: bcc023a32ea1c44a790bbf1eae473486
  depends:
  - backports.tarfile
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jaraco-context?source=hash-mapping
  size: 12483
  timestamp: 1733382698758
- conda: https://conda.anaconda.org/conda-forge/noarch/jaraco.functools-4.1.0-pyhd8ed1ab_0.conda
  sha256: 61da3e37149da5c8479c21571eaec61cc4a41678ee872dcb2ff399c30878dddb
  md5: eb257d223050a5a27f5fdf5c9debc8ec
  depends:
  - more-itertools
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jaraco-functools?source=hash-mapping
  size: 15545
  timestamp: 1733746481844
- conda: https://conda.anaconda.org/conda-forge/noarch/jeepney-0.9.0-pyhd8ed1ab_0.conda
  sha256: 00d37d85ca856431c67c8f6e890251e7cc9e5ef3724a0302b8d4a101f22aa27f
  md5: b4b91eb14fbe2f850dd2c5fc20676c0d
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jeepney?source=hash-mapping
  size: 40015
  timestamp: 1740828380668
- conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-3.10.0-he073ed8_18.conda
  sha256: a922841ad80bd7b222502e65c07ecb67e4176c4fa5b03678a005f39fcc98be4b
  md5: ad8527bf134a90e1c9ed35fa0b64318c
  constrains:
  - sysroot_linux-64 ==2.17
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  purls: []
  size: 943486
  timestamp: 1729794504440
- conda: https://conda.anaconda.org/conda-forge/noarch/keyring-25.6.0-pyha804496_0.conda
  sha256: b6f57c17cf098022c32fe64e85e9615d427a611c48a5947cdfc357490210a124
  md5: cdd58ab99c214b55d56099108a914282
  depends:
  - __linux
  - importlib-metadata >=4.11.4
  - importlib_resources
  - jaraco.classes
  - jaraco.context
  - jaraco.functools
  - jeepney >=0.4.2
  - python >=3.9
  - secretstorage >=3.2
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/keyring?source=hash-mapping
  size: 36985
  timestamp: 1735210286595
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
  sha256: db73f38155d901a610b2320525b9dd3b31e4949215c870685fd92ea61b5ce472
  md5: 01f8d123c96816249efd255a31ad7712
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 671240
  timestamp: 1740155456116
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h767d61c_2.conda
  sha256: 3a572d031cb86deb541d15c1875aaa097baefc0c580b54dc61f5edab99215792
  md5: ef504d1acbd74b7cc6849ef8af47dd03
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 14.2.0 h767d61c_2
  - libgcc-ng ==14.2.0=*_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 847885
  timestamp: 1740240653082
- conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-14.2.0-h9c4974d_102.conda
  sha256: d663727f935853d1e94b8eb69fb1bac267339c99236fd26e14d4a2297ac96c91
  md5: 80ecc6e9ecffe737e30a7e5a9b10bcb4
  depends:
  - __unix
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 2761178
  timestamp: 1740240568863
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_2.conda
  sha256: fb7558c328b38b2f9d2e412c48da7890e7721ba018d733ebdfea57280df01904
  md5: a2222a6ada71fb478682efe483ce0f92
  depends:
  - libgcc 14.2.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 53758
  timestamp: 1740240660904
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.1-h2ff4ddf_0.conda
  sha256: 18e354d30a60441b0bf5fcbb125b6b22fd0df179620ae834e2533d44d1598211
  md5: 0305434da649d4fb48a425e588b79ea6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.44,<10.45.0a0
  constrains:
  - glib 2.84.1 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3947789
  timestamp: 1743773764878
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h767d61c_2.conda
  sha256: 1a3130e0b9267e781b89399580f3163632d59fe5b0142900d63052ab1a53490e
  md5: 06d02030237f4d5b3d9a7e7d348fe3c6
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 459862
  timestamp: 1740240588123
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_0.conda
  sha256: f4f21dfc54b08d462f707b771ecce3fa9bc702a2a05b55654f64154f48b141ef
  md5: 0e87378639676987af32fee53ba32258
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: 0BSD
  purls: []
  size: 112709
  timestamp: 1743771086123
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33408
  timestamp: 1697359010159
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-14.2.0-hed042b8_2.conda
  sha256: 489e069ed0a3c376da5d83166a330c1b8a041a3d25a482f692b4fb86846f2a2d
  md5: 80f0abb70cd4f10ee15aa5693d89c65a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14.2.0
  - libstdcxx >=14.2.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 4507944
  timestamp: 1740240704883
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.1-hee588c1_2.conda
  sha256: a086289bf75c33adc1daed3f1422024504ffb5c3c8b3285c49f025c29708ed16
  md5: 962d6ac93c30b1dfc54c9cccafd1003e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 918664
  timestamp: 1742083674731
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-h8f9b012_2.conda
  sha256: 8f5bd92e4a24e1d35ba015c5252e8f818898478cb3bc50bd8b12ab54707dc4da
  md5: a78c856b6dc6bf4ea8daeb9beaaa3fb0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 14.2.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3884556
  timestamp: 1740240685253
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
  sha256: 0fbacdfb31e55964152b24d5567e9a9996e1e7902fb08eb7d91b5fd6ce60803a
  md5: fee3164ac23dfca50cfcc8b85ddefb81
  depends:
  - mdurl >=0.1,<1
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/markdown-it-py?source=hash-mapping
  size: 64430
  timestamp: 1733250550053
- conda: https://conda.anaconda.org/conda-forge/linux-64/maturin-1.8.3-py312h6ab59e4_0.conda
  sha256: 36d04c21f72cd8f597e18e5d70fc6d085b18776f6076572813c396fcb55a2e1b
  md5: 99d42f508f2af717f27e63d607688f97
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.4.1,<4.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tomli >=1.1.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/maturin?source=hash-mapping
  size: 6472955
  timestamp: 1741886678732
- conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
  sha256: 78c1bbe1723449c52b7a9df1af2ee5f005209f67e40b6e1d3c7619127c43b1c7
  md5: 592132998493b3ff25fd7479396e8351
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/mdurl?source=hash-mapping
  size: 14465
  timestamp: 1733255681319
- conda: https://conda.anaconda.org/conda-forge/noarch/more-itertools-10.6.0-pyhd8ed1ab_0.conda
  sha256: e017ede184823b12a194d058924ca26e1129975cee1cae47f69d6115c0478b55
  md5: 9b1225d67235df5411dbd2c94a5876b7
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/more-itertools?source=hash-mapping
  size: 58739
  timestamp: 1736883940984
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/linux-64/nh3-0.2.21-py39h77e2912_1.conda
  noarch: python
  sha256: 05b2fcbc831ea2936108ba1ebdb249d310d710c7880a98a25817510cf8a41d2a
  md5: 5547559fdb8becc558147c0183e5eebe
  depends:
  - python
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - _python_abi3_support 1.*
  - python >=3.9
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/nh3?source=hash-mapping
  size: 621078
  timestamp: 1741652643562
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_0.conda
  sha256: 38285d280f84f1755b7c54baf17eccf2e3e696287954ce0adca16546b85ee62c
  md5: bb539841f2a3fde210f387d00ed4bb9d
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3121673
  timestamp: 1744132167438
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-24.2-pyhd8ed1ab_2.conda
  sha256: da157b19bcd398b9804c5c52fc000fcb8ab0525bdb9c70f95beaa0bb42f85af1
  md5: 3bfed7e6228ebf2f7b9eaa47f1b4e2aa
  depends:
  - python >=3.8
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/packaging?source=hash-mapping
  size: 60164
  timestamp: 1733203368787
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.44-hba22ea6_2.conda
  sha256: 1087716b399dab91cc9511d6499036ccdc53eb29a288bebcb19cf465c51d7c0d
  md5: df359c09c41cd186fffb93a2d87aa6f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc-ng >=12
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 952308
  timestamp: 1723488734144
- conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.0.1-pyh8b19718_0.conda
  sha256: 585940f09d87787f79f73ff5dff8eb2af8a67e5bec5eebf2f553cd26c840ba69
  md5: 79b5c1440aedc5010f687048d9103628
  depends:
  - python >=3.9,<3.13.0a0
  - setuptools
  - wheel
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pip?source=hash-mapping
  size: 1256460
  timestamp: 1739142857253
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.1-pyhd8ed1ab_0.conda
  sha256: 28a3e3161390a9d23bc02b4419448f8d27679d9e2c250e29849e37749c8de86b
  md5: 232fb4577b6687b2d503ef8e254270c9
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pygments?source=hash-mapping
  size: 888600
  timestamp: 1736243563082
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pysocks?source=hash-mapping
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.10-h9e4cc4f_0_cpython.conda
  sha256: 4dc1da115805bd353bded6ab20ff642b6a15fcc72ac2f3de0e1d014ff3612221
  md5: a41d26cd4d47092d683915d058380dec
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.49.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  purls: []
  size: 31279179
  timestamp: 1744325164633
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.10-hd8ed1ab_0.conda
  sha256: 3ae92e0e6979add5c2339a2e2466fe8296aaea302c8132cfb589f3cce0e106f2
  md5: 512b45bf2297eac32afe1b57289fd4db
  depends:
  - cpython 3.12.10.*
  - python_abi * *_cp312
  license: Python-2.0
  purls: []
  size: 45831
  timestamp: 1744323227399
- conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.12-6_cp312.conda
  build_number: 6
  sha256: 09aff7ca31d1dbee63a504dba89aefa079b7c13a50dae18e1fe40a40ea71063e
  md5: 95bd67b1113859774c30418e8481f9d8
  constrains:
  - python 3.12.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6872
  timestamp: 1743483197238
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/noarch/readme_renderer-44.0-pyhd8ed1ab_1.conda
  sha256: 66f3adf6aaabf977cfcc22cb65607002b1de4a22bc9fac7be6bb774bc6f85a3a
  md5: c58dd5d147492671866464405364c0f1
  depends:
  - cmarkgfm >=0.8.0
  - docutils >=0.21.2
  - nh3 >=0.2.14
  - pygments >=2.5.1
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/readme-renderer?source=hash-mapping
  size: 17481
  timestamp: 1734339765256
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.3-pyhd8ed1ab_1.conda
  sha256: d701ca1136197aa121bbbe0e8c18db6b5c94acbd041c2b43c70e5ae104e1d8ad
  md5: a9b9368f3701a417eac9edbcae7cb737
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/requests?source=hash-mapping
  size: 58723
  timestamp: 1733217126197
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-toolbelt-1.0.0-pyhd8ed1ab_1.conda
  sha256: c0b815e72bb3f08b67d60d5e02251bbb0164905b5f72942ff5b6d2a339640630
  md5: 66de8645e324fda0ea6ef28c2f99a2ab
  depends:
  - python >=3.9
  - requests >=2.0.1,<3.0.0
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/requests-toolbelt?source=hash-mapping
  size: 44285
  timestamp: 1733734886897
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-2.0.0-pyhd8ed1ab_1.conda
  sha256: d617373ba1a5108336cb87754d030b9e384dcf91796d143fa60fe61e76e5cfb0
  md5: 43e14f832d7551e5a8910672bfc3d8c6
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/rfc3986?source=hash-mapping
  size: 38028
  timestamp: 1733921806657
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
  sha256: d10e2b66a557ec6296844e04686db87818b0df87d73c06388f2332fda3f7d2d5
  md5: 202f08242192ce3ed8bdb439ba40c0fe
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.9
  - typing_extensions >=4.0.0,<5.0.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rich?source=hash-mapping
  size: 200323
  timestamp: 1743371105291
- pypi: .
  name: rolyrs
  version: 0.1.0
  sha256: c189b2c7fb00fd8301167637908875b90f8b4143796e9d003cf9c22060bd0038
  requires_dist:
  - pip
  editable: true
- conda: https://conda.anaconda.org/conda-forge/linux-64/ruff-0.11.5-py312h286b59f_0.conda
  sha256: cf1841ea018fe0454fd6702bbc2ceed47ecc6eb6a8833bd02f5645496eb938b6
  md5: 8d601aeb30065c16564c9222287ad4a6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/ruff?source=hash-mapping
  size: 9007534
  timestamp: 1744322720648
- conda: https://conda.anaconda.org/conda-forge/linux-64/rust-1.86.0-h1a8d7c4_0.conda
  sha256: fa3b6757df927a24c3006bc5bffbac5b0c9a54b9755c08847f8a832ec1b79300
  md5: 98eab8148e1447e79f9e03492d04e291
  depends:
  - __glibc >=2.17,<3.0.a0
  - gcc_impl_linux-64
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - rust-std-x86_64-unknown-linux-gnu 1.86.0 h2c6d0dc_0
  - sysroot_linux-64 >=2.17
  license: MIT
  license_family: MIT
  purls: []
  size: 218638108
  timestamp: 1743697775334
- conda: https://conda.anaconda.org/conda-forge/noarch/rust-std-x86_64-unknown-linux-gnu-1.86.0-h2c6d0dc_0.conda
  sha256: 8c1c68b7a8ce9657fea7d266607c21c9a00a382c346348a232e539c8a3266e84
  md5: 2fcc4c775a50bd2ce3ccb8dc56e4fb47
  depends:
  - __unix
  constrains:
  - rust >=1.86.0,<1.86.1.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 37636509
  timestamp: 1743697574868
- conda: https://conda.anaconda.org/conda-forge/linux-64/secretstorage-3.3.3-py312h7900ff3_3.conda
  sha256: c6d5d0bc7fb6cbfa3b8be8f2399a3c1308b3392a4e20bd1a0f29a828fda5ab20
  md5: 4840da9db2808db946a0d979603c6de4
  depends:
  - cryptography
  - dbus
  - jeepney >=0.6
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/secretstorage?source=hash-mapping
  size: 31601
  timestamp: 1725915741329
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-78.1.0-pyhff2d567_0.conda
  sha256: d4c74d2140f2fbc72fe5320cbd65f3fd1d1f7832ab4d7825c37c38ab82440ae2
  md5: a42da9837e46c53494df0044c3eb1f53
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools?source=compressed-mapping
  size: 786557
  timestamp: 1743775941985
- conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.17-h0157908_18.conda
  sha256: 69ab5804bdd2e8e493d5709eebff382a72fab3e9af6adf93a237ccf8f7dbd624
  md5: 460eba7851277ec1fd80a1a24080787a
  depends:
  - kernel-headers_linux-64 3.10.0 he073ed8_18
  - tzdata
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  purls: []
  size: 15166921
  timestamp: 1735290488259
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3318875
  timestamp: 1699202167581
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
  sha256: 18636339a79656962723077df9a56c0ac7b8a864329eb8f847ee3d38495b863e
  md5: ac944244f1fed2eb49bae07193ae8215
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/tomli?source=hash-mapping
  size: 19167
  timestamp: 1733256819729
- conda: https://conda.anaconda.org/conda-forge/noarch/twine-6.1.0-pyh29332c3_0.conda
  sha256: c5b373f6512b96324c9607d7d91a76bb53c1056cb1012b4f9c86900c6b7f8898
  md5: d319066fad04e07a0223bf9936090161
  depends:
  - id
  - importlib-metadata >=3.6
  - keyring >=15.1
  - packaging >=24.0
  - python >=3.9
  - readme_renderer >=35.0
  - requests >=2.20
  - requests-toolbelt >=0.8.0,!=0.9.0
  - rfc3986 >=1.4.0
  - rich >=12.0.0
  - urllib3 >=1.26.0
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/twine?source=hash-mapping
  size: 40401
  timestamp: 1737553658703
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.13.2-pyh29332c3_0.conda
  sha256: a8aaf351e6461de0d5d47e4911257e25eec2fa409d71f3b643bb2f748bde1c08
  md5: 83fc6ae00127671e301c9f44254c31b8
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/typing-extensions?source=compressed-mapping
  size: 52189
  timestamp: 1744302253997
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.4.0-pyhd8ed1ab_0.conda
  sha256: a25403b76f7f03ca1a906e1ef0f88521edded991b9897e7fed56a3e334b3db8c
  md5: c1e349028e0052c4eea844e94f773065
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/urllib3?source=hash-mapping
  size: 100791
  timestamp: 1744323705540
- conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
  sha256: 1b34021e815ff89a4d902d879c3bd2040bc1bd6169b32e9427497fa05c55f1ce
  md5: 75cb7132eb58d97896e173ef12ac9986
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wheel?source=hash-mapping
  size: 62931
  timestamp: 1733130309598
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.21.0-pyhd8ed1ab_1.conda
  sha256: 567c04f124525c97a096b65769834b7acb047db24b15a56888a322bf3966c3e1
  md5: 0c3cc595284c5e8f0f9900a9b228a332
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/zipp?source=hash-mapping
  size: 21809
  timestamp: 1732827613585
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_1.conda
  sha256: b4fd6bd1cb87a183a8bbe85b4e87a1e7c51473309d0d82cd88d38fb021bcf41e
  md5: d28b82fcc8d1b462b595af4b15a6cdcf
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 731658
  timestamp: 1741853415477
