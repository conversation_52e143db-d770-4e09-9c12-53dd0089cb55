from pathlib import Path
from typing import Any, Dict, List, Optional


class OutputTracker:
    """Track and manage output files generated by RolyPoly commands.
    Maintains a Polars DataFrame with information about generated files including
    their locations, types, and the commands that created them."""

    def __init__(self):
        """Initialize an empty OutputTracker."""
        import polars as pl

        self.df = pl.DataFrame(
            schema={
                "filename": pl.Utf8,
                "absolute_path": pl.Utf8,
                "command_name": pl.Utf8,
                "command": pl.Utf8,
                "file_type": pl.Utf8,
                "file_size": pl.Int64,
                "timestamp": pl.Datetime,
                "is_merged": pl.<PERSON>,
            }
        )

    def add_file(
        self,
        filename: str,
        command_name: str,
        command: str,
        is_merged: bool,
        file_type: Optional[str] = None,
    ) -> None:
        """Add a new file to the tracker. If file_type is None, it will be guessed from extension."""
        from datetime import datetime

        import polars as pl

        absolute_path = Path(filename).resolve()
        file_size = absolute_path.stat().st_size
        file_type = file_type or self.get_file_type(filename)

        new_row = pl.DataFrame(
            {
                "filename": [str(filename)],
                "absolute_path": [str(absolute_path)],
                "command_name": [command_name],
                "command": [command],
                "file_type": [file_type],
                "file_size": [file_size],
                "timestamp": [datetime.now()],
                "is_merged": [is_merged],
            }
        )

        self.df = pl.concat([self.df, new_row])

    @staticmethod
    def get_file_type(filename: str) -> str:
        """Determine file type from filename extension."""
        ext = Path(filename).suffix.lower()
        if ext == ".gz":
            ext = Path(filename).with_suffix("").suffix.lower() + ".gz"

        file_types = {
            ".fq": "fastq",
            ".fastq": "fastq",
            ".fq.gz": "fastq_gzipped",
            ".fastq.gz": "fastq_gzipped",
            ".fa": "fasta",
            ".fasta": "fasta",
            ".fa.gz": "fasta_gzipped",
            ".fasta.gz": "fasta_gzipped",
            ".txt": "text",
            ".txt.gz": "text_gzipped",
        }

        return file_types.get(ext, "unknown")

    def get_latest_output(self, file_type: Optional[str] = None) -> Optional[str]:
        """Get the most recently added file of a specific type. If file_type is None, returns latest of any type."""
        import polars as pl

        filtered_df = (
            self.df.filter(pl.col("file_type") == file_type) if file_type else self.df
        )
        return (
            filtered_df.tail(1)["absolute_path"][0] if filtered_df.height > 0 else None
        )

    def get_latest_non_merged_file(self) -> Optional[str]:
        """Get the most recently added non-merged file."""
        import polars as pl

        non_merged_files = self.df.filter(pl.col("is_merged") == False)
        return (
            non_merged_files.tail(1)["absolute_path"][0]
            if non_merged_files.height > 0
            else None
        )

    def get_latest_merged_file(self) -> Optional[str]:
        """Get the most recently added merged file."""
        import polars as pl

        merged_files = self.df.filter(pl.col("is_merged") == True)
        return (
            merged_files.tail(1)["absolute_path"][0]
            if merged_files.height > 0
            else None
        )

    def get_file_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """Get all information about a specific file."""
        import polars as pl

        file_info = self.df.filter(pl.col("filename") == filename)
        return file_info.to_dicts()[0] if file_info.height > 0 else None

    def get_files_by_command(self, command_name: str) -> List[Dict[str, Any]]:
        """Get information about all files generated by a specific command."""
        import polars as pl

        return self.df.filter(pl.col("command_name") == command_name).to_dicts()

    def to_csv(self, output_file: str) -> None:
        """Save the tracking information to a CSV file."""
        self.df.write_csv(output_file)

    @classmethod
    def from_csv(cls, input_file: str) -> "OutputTracker":
        """Create an OutputTracker from a previously saved CSV file."""
        import polars as pl

        tracker = cls()
        tracker.df = pl.read_csv(input_file)
        return tracker
