{% set name = "rolypoly-bio" %}
{% set version = "0.3.0" %}

package:
  name: {{ name|lower }}
  version: {{ version }}

source:
  url: https://pypi.org/packages/source/{{ name[0] }}/{{ name }}/rolypoly_bio-{{ version }}.tar.gz
  sha256: 5c205b0ce972960a92b785f7fd79393ee114eacf763b5e47db38b75ff04623c3

build:
  entry_points:
    - rolypoly = rolypoly.rolypoly:rolypoly
  noarch: python
  script: {{ PYTHON }} -m pip install . -vv --no-deps --no-build-isolation
  number: 0

requirements:
  host:
    - python >=3.9
    - hatchling
    - pip
  run:
    - python >=3.9
    - bbmapy >=0.0.46,<0.0.48
    - dna_features_viewer >=3.1.4,<4
    - genomicranges >=0.6.3,<0.7
    - intervaltree >=3.1.0,<4
    - iranges >=0.4.2,<0.5
    - lightmotif >=0.9.1,<0.10
    - mappy >=2.28,<3
    - matplotlib-base >=3.10.1,<4
    - multiprocess >=0.70.0,<1
    - needletail >=0.6.3,<0.7
    - numpy >=1.24.0,<2
    - pgzip >=0.3.0,<1
    - polars >=1.26.0,<2
    - psutil >=6.1.1,<7
    - pyfastx >=2.2.0,<3
    - pyhmmer >=0.11.0,<0.12
    - pymsaviz >=0.5.0,<0.6
    - pyranges >=0.1.4,<0.2
    - pyrodigal-gv >=0.3.2,<0.4
    - requests >=2.32.3,<3
    - rich-click >=1.8.8,<2
    - rich >=14.0.0,<15
    - viennarna >=2.7.0,<3

test:
  imports:
    - rolypoly_bio
  commands:
    - pip check
    - rolypoly --help
  requires:
    - pip

about:
  summary: RNA virus analysis toolkit
  dev_url: https://code.jgi.doe.gov/UNeri/rolypoly
  license: GPL-3.0-only
  license_file: LICENSE

extra:
  recipe-maintainers:
    - UriNeri
    - Antonio
