# RolyPoly Pixi Tasks Usage Guide

This guide explains how to use the new Pixi tasks for easy RolyPoly setup and database management.

## Quick Start

### One-Command Complete Setup
```bash
# Complete setup with default database location
pixi run setup-complete

# Quick setup with custom database location  
ROLYPOLY_DATA=/path/to/your/data pixi run setup-complete
```

## Environment Configuration

### Default Database Location
The default database location is set to `$HOME/rolypoly_data` via the `pyproject.toml` configuration:

```toml
[tool.pixi.activation]
env = { ROLYPOLY_DATA = "$HOME/rolypoly_data" }
```

### Custom Database Location Options

1. **Environment Variable (Session-specific)**:
   ```bash
   export ROLYPOLY_DATA=/path/to/your/data
   pixi run setup-databases
   ```

2. **Inline Variable (Single command)**:
   ```bash
   ROLYPOLY_DATA=/path/to/your/data pixi run setup-databases
   ```

3. **Modify pyproject.toml (Permanent)**:
   Edit the `[tool.pixi.activation]` section in `pyproject.toml`:
   ```toml
   [tool.pixi.activation]
   env = { R<PERSON><PERSON><PERSON>Y_DATA = "/your/custom/path" }
   ```

## Available Pixi Tasks

### Setup Tasks

| Task | Description | Usage |
|------|-------------|-------|
| `setup-complete` | Complete setup: install + databases | `pixi run setup-complete` |
| `setup-install` | Install dependencies only | `pixi run setup-install` |
| `setup-databases` | Download databases (default location) | `pixi run setup-databases` |
| `setup-databases-custom` | Download to custom location | `pixi run setup-databases-custom /path` |
| `setup-databases-fresh` | Build from scratch (8 threads) | `pixi run setup-databases-fresh` |
| `setup-quick` | Quick setup (2 threads) | `pixi run setup-quick` |

### Database Management

| Task | Description | Usage |
|------|-------------|-------|
| `db-info` | Show database sizes | `pixi run db-info` |
| `db-update` | Update databases | `pixi run db-update` |
| `db-clean` | Remove all databases | `pixi run db-clean` |
| `db-set-location` | Set custom location | `pixi run db-set-location /path` |

### Information & Status

| Task | Description | Usage |
|------|-------------|-------|
| `info` | Show complete status | `pixi run info` |
| `status` | Alias for info | `pixi run status` |
| `verify` | Verify installation | `pixi run verify` |
| `env-show` | Show environment vars | `pixi run env-show` |
| `env-activate` | Show activation commands | `pixi run env-activate` |

### Examples & Help

| Task | Description | Usage |
|------|-------------|-------|
| `example-help` | Show RolyPoly help | `pixi run example-help` |
| `example-marker-search` | Marker search help | `pixi run example-marker-search` |
| `example-filter-reads` | Read filtering help | `pixi run example-filter-reads` |
| `example-end2end` | End-to-end pipeline help | `pixi run example-end2end` |

### Development Tasks

| Task | Description | Usage |
|------|-------------|-------|
| `dev-test` | Run tests | `pixi run dev-test` |
| `dev-format` | Format code | `pixi run dev-format` |
| `dev-check` | Check code style | `pixi run dev-check` |

### Maintenance Tasks

| Task | Description | Usage |
|------|-------------|-------|
| `clean-logs` | Remove log files | `pixi run clean-logs` |
| `clean-temp` | Remove temp files | `pixi run clean-temp` |
| `clean-all` | Clean logs + temp | `pixi run clean-all` |

## Common Usage Scenarios

### 1. First-Time Setup (Default Location)
```bash
# Clone repository
git clone https://code.jgi.doe.gov/rolypoly/rolypoly.git
cd rolypoly

# Complete setup
pixi run setup-complete

# Verify installation
pixi run verify
```

### 2. Setup with Custom Database Location
```bash
# Method 1: Environment variable
export ROLYPOLY_DATA=/data/my_rolypoly_databases
pixi run setup-complete

# Method 2: Inline variable
ROLYPOLY_DATA=/data/my_rolypoly_databases pixi run setup-complete

# Method 3: Edit pyproject.toml first, then run
pixi run setup-complete
```

### 3. Quick Development Setup
```bash
# Quick setup with minimal resources
pixi run setup-quick

# Check status
pixi run info
```

### 4. Update Existing Installation
```bash
# Update databases
pixi run db-update

# Or rebuild from scratch for latest data
pixi run setup-databases-fresh
```

### 5. Clean Installation
```bash
# Remove old databases
pixi run db-clean

# Fresh installation
pixi run setup-databases
```

## Environment Variables

### Checking Current Configuration
```bash
# Show all RolyPoly environment variables
pixi run env-show

# Show detailed status
pixi run info
```

### Setting Environment Variables

1. **Temporary (current session)**:
   ```bash
   export ROLYPOLY_DATA=/path/to/data
   ```

2. **Persistent (shell profile)**:
   ```bash
   echo 'export ROLYPOLY_DATA=/path/to/data' >> ~/.bashrc
   source ~/.bashrc
   ```

3. **Project-wide (pyproject.toml)**:
   ```toml
   [tool.pixi.activation]
   env = { 
     ROLYPOLY_DATA = "/path/to/data",
     TAXONKIT_DB = "/path/to/data/taxdump"
   }
   ```

## Database Information

### Default Database Structure
```
$ROLYPOLY_DATA/
├── hmmdbs/           # HMM profile databases (3.7 GB)
├── rRNA/             # Ribosomal RNA databases (1.3 GB)  
├── RVMT/             # RNA virus metatranscriptomes (750 MB)
├── Rfam/             # RNA family models (645 MB)
├── masking/          # Contamination sequences (503 MB)
├── taxdump/          # NCBI taxonomy (451 MB)
└── NCBI_ribovirus/   # Reference viruses (62 MB)
```

### Database Size Management
```bash
# Check database sizes
pixi run db-info

# Clean up if space is needed
pixi run db-clean

# Download only to custom location with more space
ROLYPOLY_DATA=/large/disk/path pixi run setup-databases
```

## Example Workflows

### Complete RNA Virus Analysis Workflow
```bash
# 1. Setup
export ROLYPOLY_DATA=/data/my_project/rolypoly_db
pixi run setup-complete

# 2. Verify
pixi run verify

# 3. Process reads
pixi run rolypoly filter-reads --input raw_reads.fastq.gz --output filtered/

# 4. Search for viral markers  
pixi run rolypoly marker-search --input contigs.fasta --output markers/

# 5. Run complete pipeline
pixi run rolypoly end2end --reads filtered_reads.fastq.gz --output results/
```

### Multi-User Shared Database Setup
```bash
# Admin sets up shared database
sudo mkdir -p /shared/databases/rolypoly
sudo chown user:group /shared/databases/rolypoly
ROLYPOLY_DATA=/shared/databases/rolypoly pixi run setup-complete

# Users reference shared database
export ROLYPOLY_DATA=/shared/databases/rolypoly
pixi run rolypoly --help
```

## Troubleshooting

### Common Issues

1. **"Task not found"**: Run `pixi task list` to see available tasks
2. **Database location issues**: Check `pixi run env-show`
3. **Permission errors**: Ensure write access to `$ROLYPOLY_DATA`
4. **Disk space**: Check `df -h` and use `pixi run db-info`

### Reset Everything
```bash
# Complete reset
pixi run db-clean
pixi run clean-all
pixi run setup-complete
```

## Summary

The enhanced `pyproject.toml` now provides:

✅ **One-command setup**: `pixi run setup-complete`  
✅ **Configurable database location**: Environment variable or config file  
✅ **Database management**: Update, clean, info commands  
✅ **Development tools**: Format, test, clean commands  
✅ **Status monitoring**: Info, verify, env-show commands  
✅ **Example workflows**: Help commands for all major features  

This makes RolyPoly installation and management much simpler and more flexible for different use cases and environments. 