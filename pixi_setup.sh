#!/bin/bash

# Modern RolyPoly installation script using Pi<PERSON>
# This is the recommended installation method for RolyPoly

# Prints (echo) something and also saves it to a log file
logit () {
   echo "$(date +"%Y-%m-%d %T") $1" | tee -a "${LOGFILE}"
}

# Default paths and settings
DATA_PATH="${1:-$HOME/rolypoly_data}"
LOGFILE="${2:-$HOME/RolyPoly_pixi_setup.log}"

# Convert to absolute paths
DATA_PATH=$(realpath "$DATA_PATH")
LOGFILE=$(realpath "$LOGFILE")

# Print setup information
logit "Installing RolyPoly using Pi<PERSON> with the following configuration:"
logit "  Data directory: $DATA_PATH"
logit "  Logfile: $LOGFILE"
logit "  Installation directory: $(pwd)"

# Create data directory if it doesn't exist
if [ ! -d "$DATA_PATH" ]; then
    logit "Data path does not exist. Creating it now"
    mkdir -p "$DATA_PATH"
fi

# Check if we're in a RolyPoly directory
if [ ! -f "pyproject.toml" ] || ! grep -q "rolypoly_bio" pyproject.toml; then
    logit "Error: This script must be run from a RolyPoly source directory"
    logit "Please clone the repository first:"
    logit "  git clone https://code.jgi.doe.gov/rolypoly/rolypoly.git"
    logit "  cd rolypoly"
    logit "  bash pixi_setup.sh"
    exit 1
fi

# Check if Pixi is installed
if ! command -v pixi &> /dev/null; then
    logit "Pixi not found. Installing Pixi..."
    if command -v curl &> /dev/null; then
        curl -fsSL https://pixi.sh/install.sh | bash
        export PATH="$HOME/.pixi/bin:$PATH"
    else
        logit "Error: curl is required to install Pixi. Please install curl first."
        exit 1
    fi
    
    # Check if Pixi installation was successful
    if ! command -v pixi &> /dev/null; then
        logit "Error: Pixi installation failed. Please install Pixi manually:"
        logit "  curl -fsSL https://pixi.sh/install.sh | bash"
        exit 1
    fi
    logit "Pixi installed successfully"
else
    logit "Pixi is already installed ($(pixi --version))"
fi

# Install RolyPoly dependencies
logit "Installing RolyPoly dependencies with Pixi..."
if pixi install; then
    logit "Dependencies installed successfully"
else
    logit "Error: Failed to install dependencies with Pixi"
    exit 1
fi

# Test RolyPoly installation
logit "Testing RolyPoly installation..."
if pixi run rolypoly --version > /dev/null 2>&1; then
    ROLYPOLY_VERSION=$(pixi run rolypoly --version)
    logit "RolyPoly installation successful: $ROLYPOLY_VERSION"
else
    logit "Warning: RolyPoly command test failed, but installation may still be working"
fi

# Set up external data
logit "Preparing external data..."
export ROLYPOLY_DATA="$DATA_PATH"

# Create a simple activation script
ACTIVATE_SCRIPT="$HOME/activate_rolypoly.sh"
cat > "$ACTIVATE_SCRIPT" << EOF
#!/bin/bash
# RolyPoly activation script
export ROLYPOLY_DATA="$DATA_PATH"
export PATH="$HOME/.pixi/bin:\$PATH"
cd "$(pwd)"

echo "RolyPoly environment activated!"
echo "Data directory: \$ROLYPOLY_DATA"
echo "Run commands using: pixi run rolypoly <command>"
echo "Example: pixi run rolypoly --help"
echo ""
echo "To prepare external data (first time only):"
echo "  pixi run rolypoly prepare-data --data_dir \$ROLYPOLY_DATA"
EOF

chmod +x "$ACTIVATE_SCRIPT"

logit "RolyPoly installation complete!"
logit ""
logit "To use RolyPoly:"
logit "1. Set environment variables:"
logit "   export ROLYPOLY_DATA=$DATA_PATH"
logit "2. Run RolyPoly commands using Pixi:"
logit "   pixi run rolypoly --help"
logit "   pixi run rolypoly prepare-data --data_dir \$ROLYPOLY_DATA"
logit ""
logit "Alternatively, use the activation script:"
logit "   source $ACTIVATE_SCRIPT"
logit ""
logit "For more information, see: https://pages.jgi.doe.gov/rolypoly/docs/"

# Show available commands
logit "Available RolyPoly commands:"
pixi run rolypoly --help | grep -A 20 "Commands:" | logit

echo ""
echo "RolyPoly is ready to use!"
echo "Next steps:"
echo "  1. export ROLYPOLY_DATA=$DATA_PATH"
echo "  2. pixi run rolypoly prepare-data --data_dir \$ROLYPOLY_DATA  # (download databases)"
echo "  3. pixi run rolypoly --help  # (see available commands)" 